import { K as sanitize_props, M as spread_props, N as slot, F as store_get, V as ensure_array_like, I as head, Y as attr, Z as stringify, J as escape_html, X as attr_class, W as attr_style, a8 as maybe_selected, G as unsubscribe_stores, E as pop, A as push } from "../../../../../../chunks/index.js";
import { w as writable } from "../../../../../../chunks/index3.js";
import { p as page } from "../../../../../../chunks/stores.js";
import { I as Icon } from "../../../../../../chunks/Icon.js";
import { Z as Zap, C as Chart_column, B as Brain } from "../../../../../../chunks/zap.js";
import { a as Trending_up, C as Chevron_right, B as Bot, S as Search, T as Target } from "../../../../../../chunks/trending-up.js";
import { U as User, C as Clock, D as Download } from "../../../../../../chunks/user.js";
import { h as html } from "../../../../../../chunks/html.js";
function Building_2($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"
      }
    ],
    [
      "path",
      { "d": "M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2" }
    ],
    [
      "path",
      {
        "d": "M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"
      }
    ],
    ["path", { "d": "M10 6h4" }],
    ["path", { "d": "M10 10h4" }],
    ["path", { "d": "M10 14h4" }],
    ["path", { "d": "M10 18h4" }]
  ];
  Icon($$payload, spread_props([
    { name: "building-2" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Eye($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"
      }
    ],
    [
      "circle",
      { "cx": "12", "cy": "12", "r": "3" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "eye" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function Message_square($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "message-square" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  const messages = writable([]);
  let input = "";
  let isLoading = false;
  let currentPlaceholder = "";
  let outputFormat = "executive";
  const quickStartTemplates = [
    {
      icon: Chart_column,
      title: "Company Snapshot",
      description: "Get a summary of performance, team, and competitors",
      prompt: "Provide a comprehensive company snapshot for [Company Name], including recent financial performance, leadership team overview, main competitors, and key business metrics."
    },
    {
      icon: Target,
      title: "Go-to-Market Audit",
      description: "Evaluate positioning, messaging, channels, and campaigns",
      prompt: "Analyze [Company Name]'s go-to-market strategy including their positioning, messaging, marketing channels, recent campaigns, and overall effectiveness in reaching their target audience."
    },
    {
      icon: Brain,
      title: "Brand Perception & Category",
      description: "Analyze how a brand is perceived in its space",
      prompt: "Research [Company Name]'s brand perception, category positioning, competitive differentiation, and customer sentiment. Include analysis of their brand identity and market perception."
    }
  ];
  const outputFormats = [
    {
      value: "executive",
      label: "Executive Summary",
      description: "Bullet points + key insights"
    },
    {
      value: "slide-ready",
      label: "Slide-ready",
      description: "Sections + headers for export"
    },
    {
      value: "battlecard",
      label: "Competitive Battlecard",
      description: "Strategic comparison format"
    }
  ];
  function extractInsights(content) {
    const lines = content.split("\n").filter((line) => line.trim());
    const summary = lines.slice(0, 2).join(" ").substring(0, 200) + "...";
    const insights = lines.filter((line) => line.includes("key") || line.includes("important") || line.includes("significant")).slice(0, 3);
    const badges = [];
    if (content.includes("growth") || content.includes("increase")) badges.push("↑ Trending");
    if (content.includes("insight") || content.includes("analysis")) badges.push("💡 Insight");
    if (content.includes("challenge") || content.includes("weakness")) badges.push("⚠ Weakness");
    return { summary, insights, badges };
  }
  function formatContent(content) {
    let formatted = content.replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>').replace(/^## (.*$)/gim, '<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>').replace(/^#### (.*$)/gim, '<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>').replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/\*(.*?)\*/g, '<em class="italic" style="color: var(--muted-foreground);">$1</em>').replace(/```([\s\S]*?)```/g, '<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>').replace(/`([^`]+)`/g, '<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>').replace(/\|(.+)\|/g, (match) => {
      const cells = match.split("|").filter((cell) => cell.trim()).map((cell) => cell.trim());
      return "<tr>" + cells.map((cell) => `<td class="border border-border px-3 py-2" style="color: var(--foreground);">${cell}</td>`).join("") + "</tr>";
    }).replace(/^[\s]*[-*+] (.+)$/gim, '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>').replace(/^[\s]*(\d+)\.\s+(.+)$/gim, '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: decimal;"><span class="font-medium">$1.</span> $2</li>').replace(/^> (.+)$/gim, '<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>').replace(/\[(\d+)\]/g, '<sup class="citation-number bg-primary text-primary-foreground px-1 py-0.5 rounded text-xs font-bold ml-1">[$1]</sup>').replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>').replace(/\n\n/g, '</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">').replace(/\n/g, "<br>");
    if (!formatted.startsWith("<h") && !formatted.startsWith("<p") && !formatted.startsWith("<ul") && !formatted.startsWith("<ol") && !formatted.startsWith("<blockquote")) {
      formatted = '<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">' + formatted + "</p>";
    }
    formatted = formatted.replace(/(<li[^>]*>.*?<\/li>)/gs, (match) => {
      if (match.includes("list-style-type: disc")) {
        return '<ul class="mb-4">' + match + "</ul>";
      } else if (match.includes("list-style-type: decimal")) {
        return '<ol class="mb-4">' + match + "</ol>";
      }
      return match;
    });
    if (formatted.includes("<tr>")) {
      formatted = formatted.replace(/(<tr>.*?<\/tr>)/gs, '<table class="w-full border-collapse border border-border my-4">$1</table>');
    }
    return formatted;
  }
  if (store_get($$store_subs ??= {}, "$messages", messages).length > 0) {
    setTimeout(
      () => {
        const messagesContainer = document.querySelector(".messages-container");
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
      },
      100
    );
  }
  const each_array_1 = ensure_array_like(store_get($$store_subs ??= {}, "$messages", messages));
  const each_array_4 = ensure_array_like(outputFormats);
  head($$payload, ($$payload2) => {
    $$payload2.title = `<title>Athena - AI Market Researcher</title>`;
  });
  $$payload.out += `<div class="h-screen flex flex-col" style="background: var(--background);"><div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="w-12 h-12 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);">`;
  Building_2($$payload, {
    class: "w-6 h-6",
    style: "color: var(--primary-foreground);"
  });
  $$payload.out += `<!----></div> <div><h1 class="text-3xl font-black flex items-center gap-3" style="color: var(--foreground);">`;
  Zap($$payload, {
    class: "w-8 h-8",
    style: "color: var(--primary);"
  });
  $$payload.out += `<!----> Athena</h1> <p class="text-lg font-medium" style="color: var(--muted-foreground);">Your AI market researcher</p></div></div> <div class="flex items-center space-x-4"><div class="flex items-center space-x-2 px-4 py-2 border-2" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);">`;
  Trending_up($$payload, {
    class: "w-4 h-4",
    style: "color: var(--accent-foreground);"
  });
  $$payload.out += `<!----> <span class="text-sm font-bold" style="color: var(--accent-foreground);">Real-time Data</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground"><a${attr("href", `/dashboard/${stringify(store_get($$store_subs ??= {}, "$page", page).params.envSlug)}`)} class="hover:text-foreground transition-colors">Dashboard</a> `;
  Chevron_right($$payload, { class: "w-4 h-4" });
  $$payload.out += `<!----> <span class="text-foreground font-medium">Athena</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"><div class="h-full"><div class="card-brutal p-0 chat-container h-full" style="background: var(--card);"><div class="messages-wrapper messages-container"><div class="space-y-6">`;
  if (store_get($$store_subs ??= {}, "$messages", messages).length === 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(quickStartTemplates);
    $$payload.out += `<div class="text-center py-8"><div class="flex items-center justify-center gap-2 mb-6">`;
    Zap($$payload, {
      class: "w-6 h-6",
      style: "color: var(--primary);"
    });
    $$payload.out += `<!----> <h3 class="text-2xl font-bold" style="color: var(--foreground);">Ready to Research</h3></div> <p class="font-medium mb-8 max-w-2xl mx-auto" style="color: var(--muted-foreground);">Get marketing intelligence on any company. Choose a template
                  below or ask your custom question.</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let template = each_array[$$index];
      $$payload.out += `<button class="template-card p-6 border-2 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg text-left group" style="background: var(--card); border-color: var(--border); border-radius: 0.5rem;"><div class="mb-3"><!---->`;
      template.icon?.($$payload, {
        class: "w-8 h-8",
        style: "color: var(--primary);"
      });
      $$payload.out += `<!----></div> <h4 class="text-lg font-bold mb-2 group-hover:text-primary transition-colors" style="color: var(--foreground);">${escape_html(template.title)}</h4> <p class="text-sm leading-relaxed" style="color: var(--muted-foreground);">${escape_html(template.description)}</p> <div class="flex items-center gap-1 mt-3 text-primary opacity-0 group-hover:opacity-100 transition-opacity"><span class="text-xs font-bold">Use Template</span> `;
      Chevron_right($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----></div></button>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {
    let message = each_array_1[$$index_2];
    $$payload.out += `<div${attr_class(`flex gap-4 ${stringify(message.role === "user" ? "flex-row-reverse" : "")}`)}><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"${attr_style(`background: var(--${stringify(message.role === "user" ? "primary" : "secondary")}); border-color: var(--border); box-shadow: var(--shadow-sm);`)}>`;
    if (message.role === "user") {
      $$payload.out += "<!--[-->";
      User($$payload, {
        class: "w-5 h-5",
        style: "color: var(--primary-foreground);"
      });
    } else {
      $$payload.out += "<!--[!-->";
      Bot($$payload, {
        class: "w-5 h-5",
        style: "color: var(--secondary-foreground);"
      });
    }
    $$payload.out += `<!--]--></div> <div class="flex-1 max-w-3xl"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);">${escape_html(message.role === "user" ? "You" : "Athena")}</span> <div class="flex items-center gap-1">`;
    Clock($$payload, {
      class: "w-3 h-3",
      style: "color: var(--muted-foreground);"
    });
    $$payload.out += `<!----> <span class="text-xs" style="color: var(--muted-foreground);">${escape_html(message.timestamp.toLocaleTimeString())}</span></div> `;
    if (message.role === "assistant" && message.isReport) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1" title="Download as Markdown">`;
      Download($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Download</button>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div> `;
    if (message.role === "user") {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="p-4 border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium" style="color: var(--primary-foreground);">${escape_html(message.content)}</p></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      const insights = extractInsights(message.content);
      $$payload.out += `<div class="border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;">`;
      if (insights.summary) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<div class="p-4 border-b-2 border-border bg-muted/50"><div class="flex items-start gap-2"><span class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground rounded">TL;DR</span> <div class="text-sm font-medium formatted-summary" style="color: var(--foreground);">${html(formatContent(insights.summary))}</div></div></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (insights.badges.length > 0) {
        $$payload.out += "<!--[-->";
        const each_array_2 = ensure_array_like(insights.badges);
        $$payload.out += `<div class="px-6 pt-4 pb-2"><div class="flex flex-wrap gap-2"><!--[-->`;
        for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {
          let badge = each_array_2[$$index_1];
          $$payload.out += `<span class="text-xs font-bold px-2 py-1 border border-border rounded" style="background: var(--accent); color: var(--accent-foreground);">${escape_html(badge)}</span>`;
        }
        $$payload.out += `<!--]--></div></div>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <div class="p-6"><div class="formatted-content max-w-none">${html(formatContent(message.content))}</div></div> <div class="px-6 pb-4 border-t border-border"><div class="flex flex-wrap gap-2 mt-4"><button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1">`;
      Eye($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Compare with competitor</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1">`;
      Chart_column($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Add visuals</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1">`;
      Message_square($$payload, { class: "w-3 h-3" });
      $$payload.out += `<!----> Turn into slides</button></div></div></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  }
  $$payload.out += `<!--]--> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="input-wrapper p-6"><div class="flex items-center gap-2 mb-4"><span class="text-sm font-bold" style="color: var(--muted-foreground);">Format:</span> <select class="px-3 py-1 text-sm border-2 border-border bg-card text-foreground font-medium rounded" style="border-radius: 0.375rem;">`;
  $$payload.select_value = outputFormat;
  $$payload.out += `<!--[-->`;
  for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
    let format = each_array_4[$$index_4];
    $$payload.out += `<option${attr("value", format.value)}${maybe_selected($$payload, format.value)}>${escape_html(format.label)}</option>`;
  }
  $$payload.out += `<!--]-->`;
  $$payload.select_value = void 0;
  $$payload.out += `</select></div> <div class="relative"><div class="spotlight-input-container"><textarea${attr("placeholder", currentPlaceholder)} class="spotlight-input flex-1 resize-none min-h-[120px] p-6 pr-32 text-lg"${attr("disabled", isLoading, true)}>`;
  const $$body = escape_html(input);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea> <button${attr("disabled", !input.trim() || isLoading, true)} class="spotlight-button">`;
  {
    $$payload.out += "<!--[!-->";
    Search($$payload, { class: "w-5 h-5" });
  }
  $$payload.out += `<!--]--> <span class="font-bold">Research</span></button></div></div></div></div></div></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};

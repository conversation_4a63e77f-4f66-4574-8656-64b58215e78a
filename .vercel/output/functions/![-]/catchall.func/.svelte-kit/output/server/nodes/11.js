import * as server from '../entries/pages/(admin)/dashboard/_envSlug_/(menu)/_page.server.ts.js';

export const index = 11;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/(admin)/dashboard/_envSlug_/(menu)/_page.svelte.js')).default;
export { server };
export const server_id = "src/routes/(admin)/dashboard/[envSlug]/(menu)/+page.server.ts";
export const imports = ["_app/immutable/nodes/11.DLAhWrUD.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/Cvx8ZW61.js","_app/immutable/chunks/wnqW1tdD.js","_app/immutable/chunks/CfBaWyh2.js","_app/immutable/chunks/CDPCzm7q.js","_app/immutable/chunks/yk44OJLy.js","_app/immutable/chunks/rh_XW2Tv.js","_app/immutable/chunks/Bz0_kaay.js","_app/immutable/chunks/BxG_UISn.js","_app/immutable/chunks/D5ITLM2v.js","_app/immutable/chunks/BvpDAKCq.js","_app/immutable/chunks/hI_ygoix.js","_app/immutable/chunks/idBKwYq8.js","_app/immutable/chunks/CSxrUSuj.js","_app/immutable/chunks/jYZUw_FW.js","_app/immutable/chunks/CVCfOWck.js"];
export const stylesheets = [];
export const fonts = [];

import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{o as ht}from"../chunks/CfBaWyh2.js";import{b as wr,e as je,a as f,p as ot,f as _,c as t,r,n as B,s as o,t as G,j as e,J as Ae,o as Br,k as c,g as at,a7 as _t,l as wt,h as kt,$ as $t,m as ce,i as V,d as dt,aU as St}from"../chunks/wnqW1tdD.js";import{s as E,e as O,r as pt,h as Mt}from"../chunks/CDPCzm7q.js";import{i as z}from"../chunks/BjRbZGyQ.js";import{e as mr,i as Tr}from"../chunks/CsnEE4l9.js";import{h as mt}from"../chunks/D8kkBG2G.js";import{c as Ct}from"../chunks/ojdN50pv.js";import{r as fr,b as st,c as ye,s as rt}from"../chunks/rh_XW2Tv.js";import{s as ve}from"../chunks/Bz0_kaay.js";import{t as Yr}from"../chunks/sBNKiCwy.js";import{b as Ze}from"../chunks/CJ-FD9ng.js";import{i as At}from"../chunks/BxG_UISn.js";import{a as Dt,s as ct}from"../chunks/D5ITLM2v.js";import{w as Lt}from"../chunks/BvpDAKCq.js";import{p as Kt}from"../chunks/CGMPj6U1.js";import{s as tt,f as ft}from"../chunks/CiB29Aqe.js";import{l as kr,s as $r,p as _r}from"../chunks/Cmdkv-7M.js";import{S as gt}from"../chunks/DrB3LJpu.js";import{C as bt}from"../chunks/ZAWXEYb0.js";import{s as Sr}from"../chunks/BDqVm3Gq.js";import{I as Mr}from"../chunks/CX_t0Ed_.js";import{L as Jr}from"../chunks/CDkJ1nAx.js";import{a as Wr,T as qr,C as Et,U as Rt,B as vt,S as Ot}from"../chunks/JZlAgLJx.js";import{D as nt}from"../chunks/BVqp3nk9.js";import{X as jt}from"../chunks/BAo0SQ6-.js";function Tt(Z,w){const g=kr(w,["children","$$slots","$$events","$$legacy"]),q=[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"}]];Mr(Z,$r({name:"book-open"},()=>g,{get iconNode(){return q},children:(F,J)=>{var S=wr(),M=je(S);Sr(M,w,"default",{},null),f(F,S)},$$slots:{default:!0}}))}function ut(Z,w){const g=kr(w,["children","$$slots","$$events","$$legacy"]),q=[["circle",{cx:"12",cy:"12",r:"10"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16"}]];Mr(Z,$r({name:"circle-alert"},()=>g,{get iconNode(){return q},children:(F,J)=>{var S=wr(),M=je(S);Sr(M,w,"default",{},null),f(F,S)},$$slots:{default:!0}}))}function yt(Z,w){const g=kr(w,["children","$$slots","$$events","$$legacy"]),q=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"m9 12 2 2 4-4"}]];Mr(Z,$r({name:"circle-check"},()=>g,{get iconNode(){return q},children:(F,J)=>{var S=wr(),M=je(S);Sr(M,w,"default",{},null),f(F,S)},$$slots:{default:!0}}))}function xt(Z,w){const g=kr(w,["children","$$slots","$$events","$$legacy"]),q=[["circle",{cx:"12",cy:"12",r:"10"}]];Mr(Z,$r({name:"circle"},()=>g,{get iconNode(){return q},children:(F,J)=>{var S=wr(),M=je(S);Sr(M,w,"default",{},null),f(F,S)},$$slots:{default:!0}}))}function it(Z,w){const g=kr(w,["children","$$slots","$$events","$$legacy"]),q=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"}]];Mr(Z,$r({name:"copy"},()=>g,{get iconNode(){return q},children:(F,J)=>{var S=wr(),M=je(S);Sr(M,w,"default",{},null),f(F,S)},$$slots:{default:!0}}))}function zt(Z,w){const g=kr(w,["children","$$slots","$$events","$$legacy"]),q=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4"}],["path",{d:"M12 18v-6"}],["path",{d:"m9 15 3 3 3-3"}]];Mr(Z,$r({name:"file-down"},()=>g,{get iconNode(){return q},children:(F,J)=>{var S=wr(),M=je(S);Sr(M,w,"default",{},null),f(F,S)},$$slots:{default:!0}}))}function lt(Z,w){const g=kr(w,["children","$$slots","$$events","$$legacy"]),q=[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"}]];Mr(Z,$r({name:"filter"},()=>g,{get iconNode(){return q},children:(F,J)=>{var S=wr(),M=je(S);Sr(M,w,"default",{},null),f(F,S)},$$slots:{default:!0}}))}function Ft(Z,w){const g=kr(w,["children","$$slots","$$events","$$legacy"]),q=[["path",{d:"M5 12h14"}],["path",{d:"M12 5v14"}]];Mr(Z,$r({name:"plus"},()=>g,{get iconNode(){return q},children:(F,J)=>{var S=wr(),M=je(S);Sr(M,w,"default",{},null),f(F,S)},$$slots:{default:!0}}))}var Pt=_('<button class="p-3 border-2 text-left hover:border-primary transition-all" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between"><div><p class="font-medium text-sm" style="color: var(--foreground);"> </p> <p class="text-xs mt-1" style="color: var(--muted-foreground);"> </p></div> <!></div></button>'),Ut=_('<div class="mt-4 p-4 border-2 space-y-4" style="background: var(--muted); border-color: var(--border);"><div class="grid md:grid-cols-2 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Industry/Niche</label> <input placeholder="e.g., E-commerce, SaaS, Healthcare" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Location</label> <select class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>United States</option><option>United Kingdom</option><option>Canada</option><option>Australia</option><option>Global</option></select></div></div> <div class="grid md:grid-cols-3 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Min Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Difficulty</label> <input type="number" max="100" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div></div></div>'),It=_("<!> Discovering Keywords...",1),Nt=_("<!> Discover Niche Keywords",1),Gt=_('<tr class="border-b hover:bg-muted/50" style="border-color: var(--border);"><td class="py-2 px-3 text-sm" style="color: var(--foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="px-2 py-1 text-xs rounded"> </span></td><td class="py-2 px-3 text-sm text-center"><span class="text-xs"> </span></td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="font-medium" style="color: var(--primary);"> </span></td></tr>'),Vt=_('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between mb-4"><h4 class="font-bold" style="color: var(--foreground);"> </h4> <div class="flex gap-2"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button></div></div> <div class="overflow-x-auto"><table class="w-full"><thead><tr class="border-b" style="border-color: var(--border);"><th class="text-left py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Keyword</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Volume</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Difficulty</th><th class="text-center py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Competition</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">CPC</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Opportunity</th></tr></thead><tbody></tbody></table></div></div>'),Bt=_('<div class="space-y-6"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="w-10 h-10 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border);"><!></div> <div><h3 class="text-lg font-bold" style="color: var(--foreground);">Niche Keyword Discovery</h3> <p class="text-sm" style="color: var(--muted-foreground);">Find untapped long-tail keywords in your specific niche</p></div></div></div> <div class="grid md:grid-cols-2 gap-3"></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Seed Keywords (comma-separated, max 20)</label> <textarea placeholder="e.g., organic coffee, fair trade coffee beans, specialty coffee roasters" class="w-full p-3 border-2 min-h-[80px]" style="background: var(--background); border-color: var(--border); color: var(--foreground);"></textarea></div> <div><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Advanced Filters <span>▼</span></button> <!></div> <button class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"><!></button> <!></div>');function Yt(Z,w){ot(w,!0);let g=_r(w,"isLoading",3,!1),q=_r(w,"discoveredKeywords",19,()=>[]),F=Ae(""),J=Ae(""),S=Ae("United States"),M=Ae(100),D=Ae(1e4),re=Ae(50),ue=Ae(!1);const qe=[{name:"E-commerce",seeds:"organic skincare, natural beauty products, vegan cosmetics"},{name:"SaaS",seeds:"project management, team collaboration, task tracking"},{name:"Local Business",seeds:"coffee shop, specialty coffee, local cafe"},{name:"Health & Wellness",seeds:"yoga classes, meditation app, wellness coaching"}];function Je(){const b=e(F).split(",").map(R=>R.trim()).filter(R=>R.length>0);if(b.length===0)return;const L={industry:e(J),location:e(S),volumeRange:{min:e(M),max:e(D)},maxDifficulty:e(re)};w.onAnalyze(b,L)}function te(b){c(F,b,!0)}function Te(){const b=e(Se).map(L=>L.keyword).join(`
`);navigator.clipboard.writeText(b)}function gr(){const b=["Keyword","Search Volume","Difficulty","Competition","CPC","Opportunity Score"],L=e(Se).map(U=>{var ie;return[U.keyword,U.search_volume,U.difficulty,U.competition,U.cpc.toFixed(2),((ie=U.opportunity_score)==null?void 0:ie.toFixed(2))||""]}),R=[b,...L].map(U=>U.join(",")).join(`
`),oe=new Blob([R],{type:"text/csv"}),se=URL.createObjectURL(oe),Q=document.createElement("a");Q.href=se,Q.download=`niche-keywords-${new Date().toISOString().split("T")[0]}.csv`,Q.click(),URL.revokeObjectURL(se)}const ze=Br(()=>q().map(b=>({...b,opportunity_score:b.search_volume/(b.difficulty+1)*(b.competition==="LOW"?2:b.competition==="MEDIUM"?1:.5)}))),Se=Br(()=>e(ze).filter(b=>b.search_volume>=e(M)&&b.search_volume<=e(D)).filter(b=>b.difficulty<=e(re)).sort((b,L)=>(L.opportunity_score||0)-(b.opportunity_score||0)));var Ne=Bt(),ae=t(Ne),Cr=t(ae),ir=t(Cr),xe=t(ir);gt(xe,{class:"w-5 h-5",style:"color: var(--primary-foreground);"}),r(ir),B(2),r(Cr),r(ae);var Ge=o(ae,2);mr(Ge,21,()=>qe,Tr,(b,L)=>{var R=Pt(),oe=t(R),se=t(oe),Q=t(se),U=t(Q,!0);r(Q);var ie=o(Q,2),Me=t(ie,!0);r(ie),r(se);var Pe=o(se,2);bt(Pe,{class:"w-4 h-4",style:"color: var(--muted-foreground);"}),r(oe),r(R),G(()=>{R.disabled=g(),E(U,e(L).name),E(Me,e(L).seeds)}),O("click",R,()=>te(e(L).seeds)),f(b,R)}),r(Ge);var Fe=o(Ge,2),Ve=o(t(Fe),2);pt(Ve),r(Fe);var Qe=o(Fe,2),Xe=t(Qe),er=t(Xe);lt(er,{class:"w-4 h-4"});var Be=o(er,2);r(Xe);var Ar=o(Xe,2);{var Dr=b=>{var L=Ut(),R=t(L),oe=t(R),se=o(t(oe),2);fr(se),r(oe);var Q=o(oe,2),U=o(t(Q),2),ie=t(U);ie.value=ie.__value="United States";var Me=o(ie);Me.value=Me.__value="United Kingdom";var Pe=o(Me);Pe.value=Pe.__value="Canada";var pe=o(Pe);pe.value=pe.__value="Australia";var tr=o(pe);tr.value=tr.__value="Global",r(U),r(Q),r(R);var lr=o(R,2),or=t(lr),X=o(t(or),2);fr(X),r(or);var De=o(or,2),me=o(t(De),2);fr(me),r(De);var br=o(De,2),he=o(t(br),2);fr(he),r(br),r(lr),r(L),G(()=>{se.disabled=g(),U.disabled=g(),X.disabled=g(),me.disabled=g(),he.disabled=g()}),Ze(se,()=>e(J),_e=>c(J,_e)),st(U,()=>e(S),_e=>c(S,_e)),Ze(X,()=>e(M),_e=>c(M,_e)),Ze(me,()=>e(D),_e=>c(D,_e)),Ze(he,()=>e(re),_e=>c(re,_e)),f(b,L)};z(Ar,b=>{e(ue)&&b(Dr)})}r(Qe);var rr=o(Qe,2),zr=t(rr);{var Fr=b=>{var L=It(),R=je(L);Jr(R,{class:"w-4 h-4 animate-spin"}),B(),f(b,L)},Ir=b=>{var L=Nt(),R=je(L);Wr(R,{class:"w-4 h-4"}),B(),f(b,L)};z(zr,b=>{g()?b(Fr):b(Ir,!1)})}r(rr);var Nr=o(rr,2);{var Lr=b=>{var L=Vt(),R=t(L),oe=t(R),se=t(oe);r(oe);var Q=o(oe,2),U=t(Q),ie=t(U);it(ie,{class:"w-3 h-3"}),B(),r(U);var Me=o(U,2),Pe=t(Me);nt(Pe,{class:"w-3 h-3"}),B(),r(Me),r(Q),r(R);var pe=o(R,2),tr=t(pe),lr=o(t(tr));mr(lr,21,()=>e(Se),Tr,(or,X)=>{var De=Gt(),me=t(De),br=t(me,!0);r(me);var he=o(me),_e=t(he,!0);r(he);var dr=o(he),Kr=t(dr),Er=t(Kr,!0);r(Kr),r(dr);var yr=o(dr),n=t(yr),u=t(n,!0);r(n),r(yr);var y=o(yr),j=t(y);r(y);var K=o(y),Y=t(K),a=t(Y,!0);r(Y),r(K),r(De),G((s,v,d)=>{E(br,e(X).keyword),E(_e,s),ye(Kr,`background: ${e(X).difficulty<30?"var(--primary)":e(X).difficulty<60?"#fbbf24":"#ef4444"}; color: white;`),E(Er,e(X).difficulty),ye(n,`color: ${e(X).competition==="LOW"?"var(--primary)":e(X).competition==="MEDIUM"?"#fbbf24":"#ef4444"};`),E(u,e(X).competition),E(j,`$${v??""}`),E(a,d)},[()=>e(X).search_volume.toLocaleString(),()=>e(X).cpc.toFixed(2),()=>{var s;return((s=e(X).opportunity_score)==null?void 0:s.toFixed(1))||"-"}]),f(or,De)}),r(lr),r(tr),r(pe),r(L),G(()=>E(se,`Discovered Keywords (${e(Se).length??""})`)),O("click",U,Te),O("click",Me,gr),f(b,L)};z(Nr,b=>{e(Se).length>0&&b(Lr)})}r(Ne),G(b=>{Ve.disabled=g(),Xe.disabled=g(),ve(Be,1,`text-xs ${e(ue)?"rotate-180":""} transition-transform`),rr.disabled=b},[()=>!e(F).trim()||g()]),Ze(Ve,()=>e(F),b=>c(F,b)),O("click",Xe,()=>c(ue,!e(ue))),O("click",rr,Je),f(Z,Ne),at()}var Wt=_('<button class="btn-secondary p-3"><!></button>'),Ht=_('<div class="flex gap-2"><input class="flex-1 p-3 border-2" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/> <!></div>'),Zt=_('<button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Add Competitor</button>'),qt=_('<div class="mt-4 p-4 border-2 space-y-4" style="background: var(--muted); border-color: var(--border);"><div class="grid md:grid-cols-3 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Location</label> <select class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>United States</option><option>United Kingdom</option><option>Canada</option><option>Australia</option><option>Global</option></select></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Min Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Difficulty</label> <input type="number" max="100" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div></div></div>'),Jt=_("<!> Analyzing Competitor Gaps...",1),Qt=_("<!> Analyze Competitor Gaps",1),Xt=_("<div><!></div>"),eo=_('<div class="mt-2 h-1 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div>'),ro=_('<div class="flex items-start gap-3"><div class="flex-shrink-0 mt-0.5"><!></div> <div class="flex-1"><h5 class="text-sm font-bold mb-1"> </h5> <p class="text-xs"> </p> <!></div></div>'),to=_('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><h4 class="font-bold mb-4" style="color: var(--foreground);">Analyzing Competitor Gaps</h4> <div class="space-y-4"></div> <div class="mt-6 pt-4 border-t" style="border-color: var(--border);"><div class="flex items-center justify-between mb-2"><span class="text-xs font-medium" style="color: var(--muted-foreground);">Overall Progress</span> <span class="text-xs font-bold" style="color: var(--foreground);"> </span></div> <div class="h-2 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div></div></div>'),oo=_('<div class="flex items-center justify-between p-2 border" style="background: var(--background); border-color: var(--border);"><span class="text-sm font-medium" style="color: var(--foreground);"> </span> <div class="flex items-center gap-3 text-xs"><span style="color: var(--muted-foreground);"> </span> <span class="px-2 py-1 rounded" style="background: var(--primary); color: var(--primary-foreground);"> </span></div></div>'),ao=_('<div class="border-2 p-4" style="background: var(--accent); border-color: var(--border);"><div class="flex items-center gap-2 mb-3"><!> <h4 class="font-bold" style="color: var(--accent-foreground);">Quick Win Opportunities</h4></div> <div class="space-y-2"></div></div>'),so=_(`<div class="border-2 p-4 mb-4" style="background: #fef3c7; border-color: #f59e0b;"><div class="flex items-center gap-2 mb-2"><!> <h4 class="font-bold text-sm" style="color: #78350f;">Demo Data Notice</h4></div> <p class="text-sm" style="color: #78350f;">You're viewing sample data. To get real keyword analysis for your
        domains, please configure your DataForSEO API credentials in the
        environment settings.</p> <p class="text-xs mt-2" style="color: #78350f;">The keywords shown below are generic examples and not specific to your
        actual domain.</p></div>`),no=_('<span class="text-xs font-normal px-2 py-1 rounded ml-2" style="background: #fef3c7; color: #78350f;">DEMO DATA</span>'),io=_('<tr class="border-b hover:bg-muted/50" style="border-color: var(--border);"><td class="py-2 px-3 text-sm" style="color: var(--foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="px-2 py-1 text-xs rounded"> </span></td><td class="py-2 px-3 text-sm text-center"><span class="text-xs px-2 py-1 rounded"> </span></td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="font-medium" style="color: var(--primary);"> </span></td></tr>'),lo=_('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between mb-4"><h4 class="font-bold" style="color: var(--foreground);"> <!></h4> <div class="flex gap-2"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button></div></div> <div class="overflow-x-auto"><table class="w-full"><thead><tr class="border-b" style="border-color: var(--border);"><th class="text-left py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Keyword</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Volume</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Difficulty</th><th class="text-center py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Gap Type</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Competitor Pos.</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Your Pos.</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Opportunity</th></tr></thead><tbody></tbody></table></div></div>'),co=_('<div class="border-2 p-6 mt-6" style="background: var(--card); border-color: var(--border);"><h3 class="text-lg font-bold mb-4" style="color: var(--foreground);">📊 Complete SEO Strategy Analysis</h3> <div class="prose prose-sm max-w-none" style="color: var(--muted-foreground);"><div class="ai-response-content"><!></div></div></div>'),vo=_(`<div class="space-y-6"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="w-10 h-10 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border);"><!></div> <div><h3 class="text-lg font-bold" style="color: var(--foreground);">Competitor Gap Analysis</h3> <p class="text-sm" style="color: var(--muted-foreground);">Find keyword opportunities your competitors are ranking for</p></div></div></div> <div class="space-y-4"><div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Your Domain</label> <input placeholder="example.com" class="w-full p-3 border-2" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Competitor Domains (up to 3)</label> <div class="space-y-2"><!> <!></div></div></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Analysis Type</label> <div class="grid md:grid-cols-3 gap-3"><button><p class="font-medium text-sm" style="color: var(--foreground);">Missing Keywords</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Keywords competitors rank for but you don't</p></button> <button><p class="font-medium text-sm" style="color: var(--foreground);">Lower Rankings</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Keywords where competitors outrank you</p></button> <button><p class="font-medium text-sm" style="color: var(--foreground);">All Gaps</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Show all keyword opportunities</p></button></div></div> <div><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Advanced Filters <span>▼</span></button> <!></div> <button class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"><!></button> <!> <!> <!> <!> <!></div>`);function uo(Z,w){ot(w,!0);let g=_r(w,"isLoading",3,!1),q=_r(w,"gapKeywords",19,()=>[]),F=_r(w,"progressSteps",19,()=>[]),J=_r(w,"currentProgress",3,0),S=_r(w,"isMockData",3,!1),M=_r(w,"aiResponse",3,""),D=Ae(""),re=Ae(_t([""])),ue=Ae("United States"),qe=Ae(500),Je=Ae(70),te=Ae("all"),Te=Ae(!1);function gr(){e(re).length<3&&c(re,[...e(re),""],!0)}function ze(n){c(re,e(re).filter((u,y)=>y!==n),!0)}function Se(){const n=e(re).filter(y=>y.trim().length>0);if(!e(D).trim()||n.length===0)return;const u={yourDomain:e(D).trim(),competitors:n,location:e(ue),minVolume:e(qe),maxDifficulty:e(Je),gapType:e(te)};w.onAnalyze(u)}function Ne(){const n=e(xe).map(u=>u.keyword).join(`
`);navigator.clipboard.writeText(n)}function ae(n){return n.replace(/^### (.*$)/gim,'<h3 class="text-lg font-bold mb-2 mt-4" style="color: var(--foreground);">$1</h3>').replace(/^## (.*$)/gim,'<h2 class="text-xl font-bold mb-3 mt-6" style="color: var(--foreground);">$1</h2>').replace(/^# (.*$)/gim,'<h1 class="text-2xl font-bold mb-4" style="color: var(--foreground);">$1</h1>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/^[\-\*•] (.*$)/gim,'<li class="ml-6 mb-1 list-disc">$1</li>').replace(/\|(.+)\|/g,(y,j)=>{const K=j.split("|").map(s=>s.trim()).filter(s=>s.length>0);return j.includes("---")?"":`<tr>${K.map(s=>`<td class="border px-3 py-2 text-sm" style="border-color: var(--border);">${s}</td>`).join("")}</tr>`}).replace(/(<tr>.*<\/tr>)/gs,'<table class="w-full border-collapse border mt-4 mb-4" style="border-color: var(--border);">$1</table>').replace(/\n/g,"<br>")}function Cr(){const n=["Keyword","Search Volume","Difficulty","Competition","CPC","Competitor Position","Your Position","Gap Type","Opportunity Score"],u=e(xe).map(a=>{var s;return[a.keyword,a.search_volume,a.difficulty,a.competition,a.cpc.toFixed(2),a.competitor_position,a.your_position||"Not ranking",a.gap_type,((s=a.opportunity_score)==null?void 0:s.toFixed(2))||""]}),y=[n,...u].map(a=>a.join(",")).join(`
`),j=new Blob([y],{type:"text/csv"}),K=URL.createObjectURL(j),Y=document.createElement("a");Y.href=K,Y.download=`competitor-gap-analysis-${new Date().toISOString().split("T")[0]}.csv`,Y.click(),URL.revokeObjectURL(K)}const ir=Br(()=>q().map(n=>({...n,opportunity_score:n.search_volume/(n.difficulty+1)*(n.gap_type==="missing"?2:1)}))),xe=Br(()=>e(ir).filter(n=>n.search_volume>=e(qe)).filter(n=>n.difficulty<=e(Je)).filter(n=>e(te)==="all"||n.gap_type===e(te)).sort((n,u)=>(u.opportunity_score||0)-(n.opportunity_score||0))),Ge=Br(()=>e(xe).filter(n=>n.search_volume>1e3&&n.difficulty<30).slice(0,5));var Fe=vo(),Ve=t(Fe),Qe=t(Ve),Xe=t(Qe),er=t(Xe);qr(er,{class:"w-5 h-5",style:"color: var(--primary-foreground);"}),r(Xe),B(2),r(Qe),r(Ve);var Be=o(Ve,2),Ar=t(Be),Dr=o(t(Ar),2);fr(Dr),r(Ar);var rr=o(Ar,2),zr=o(t(rr),2),Fr=t(zr);mr(Fr,17,()=>e(re),Tr,(n,u,y)=>{var j=Ht(),K=t(j);fr(K),rt(K,"placeholder",`competitor${y+1}.com`);var Y=o(K,2);{var a=s=>{var v=Wt(),d=t(v);jt(d,{class:"w-4 h-4"}),r(v),G(()=>v.disabled=g()),O("click",v,()=>ze(y)),f(s,v)};z(Y,s=>{e(re).length>1&&s(a)})}r(j),G(()=>K.disabled=g()),Ze(K,()=>e(re)[y],s=>e(re)[y]=s),f(n,j)});var Ir=o(Fr,2);{var Nr=n=>{var u=Zt(),y=t(u);Ft(y,{class:"w-4 h-4"}),B(),r(u),G(()=>u.disabled=g()),O("click",u,gr),f(n,u)};z(Ir,n=>{e(re).length<3&&n(Nr)})}r(zr),r(rr),r(Be);var Lr=o(Be,2),b=o(t(Lr),2),L=t(b),R=o(L,2),oe=o(R,2);r(b),r(Lr);var se=o(Lr,2),Q=t(se),U=t(Q);lt(U,{class:"w-4 h-4"});var ie=o(U,2);r(Q);var Me=o(Q,2);{var Pe=n=>{var u=qt(),y=t(u),j=t(y),K=o(t(j),2),Y=t(K);Y.value=Y.__value="United States";var a=o(Y);a.value=a.__value="United Kingdom";var s=o(a);s.value=s.__value="Canada";var v=o(s);v.value=v.__value="Australia";var d=o(v);d.value=d.__value="Global",r(K),r(j);var i=o(j,2),m=o(t(i),2);fr(m),r(i);var p=o(i,2),l=o(t(p),2);fr(l),r(p),r(y),r(u),G(()=>{K.disabled=g(),m.disabled=g(),l.disabled=g()}),st(K,()=>e(ue),x=>c(ue,x)),Ze(m,()=>e(qe),x=>c(qe,x)),Ze(l,()=>e(Je),x=>c(Je,x)),f(n,u)};z(Me,n=>{e(Te)&&n(Pe)})}r(se);var pe=o(se,2),tr=t(pe);{var lr=n=>{var u=Jt(),y=je(u);Jr(y,{class:"w-4 h-4 animate-spin"}),B(),f(n,u)},or=n=>{var u=Qt(),y=je(u);Wr(y,{class:"w-4 h-4"}),B(),f(n,u)};z(tr,n=>{g()?n(lr):n(or,!1)})}r(pe);var X=o(pe,2);{var De=n=>{var u=to(),y=o(t(u),2);mr(y,21,F,d=>d.id,(d,i)=>{var m=ro(),p=t(m),l=t(p);{var x=h=>{var ee=Xt(),de=t(ee);yt(de,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),r(ee),Yr(3,ee,()=>ft,()=>({duration:200})),f(h,ee)},I=(h,ee)=>{{var de=Le=>{Jr(Le,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},ar=Le=>{xt(Le,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};z(h,Le=>{e(i).status==="active"?Le(de):Le(ar,!1)},ee)}};z(l,h=>{e(i).status==="completed"?h(x):h(I,!1)})}r(p);var C=o(p,2),T=t(C),k=t(T,!0);r(T);var A=o(T,2),W=t(A,!0);r(A);var fe=o(A,2);{var le=h=>{var ee=eo(),de=t(ee);r(ee),G(()=>ye(de,`background: var(--primary); width: ${e(i).progress??""}%`)),f(h,ee)};z(fe,h=>{e(i).status==="active"&&e(i).progress&&h(le)})}r(C),r(m),G(()=>{ye(T,`color: ${e(i).status==="pending"?"var(--muted-foreground)":"var(--foreground)"};
                         opacity: ${e(i).status==="pending"?"0.5":"1"}`),E(k,e(i).title),ye(A,`color: var(--muted-foreground);
                        opacity: ${e(i).status==="pending"?"0.5":"1"}`),E(W,e(i).description)}),Yr(3,m,()=>tt,()=>({duration:300})),f(d,m)}),r(y);var j=o(y,2),K=t(j),Y=o(t(K),2),a=t(Y);r(Y),r(K);var s=o(K,2),v=t(s);r(s),r(j),r(u),G(()=>{E(a,`${J()??""}%`),ye(v,`background: linear-gradient(to right, var(--primary), var(--accent)); 
                      width: ${J()??""}%`)}),Yr(3,u,()=>tt,()=>({duration:300})),f(n,u)};z(X,n=>{g()&&F().length>0&&n(De)})}var me=o(X,2);{var br=n=>{var u=ao(),y=t(u),j=t(y);ut(j,{class:"w-5 h-5",style:"color: var(--accent-foreground);"}),B(2),r(y);var K=o(y,2);mr(K,21,()=>e(Ge),Tr,(Y,a)=>{var s=oo(),v=t(s),d=t(v,!0);r(v);var i=o(v,2),m=t(i),p=t(m);r(m);var l=o(m,2),x=t(l);r(l),r(i),r(s),G(I=>{E(d,e(a).keyword),E(p,`Vol: ${I??""}`),E(x,`Difficulty: ${e(a).difficulty??""}`)},[()=>e(a).search_volume.toLocaleString()]),f(Y,s)}),r(K),r(u),f(n,u)};z(me,n=>{e(Ge).length>0&&!g()&&n(br)})}var he=o(me,2);{var _e=n=>{var u=so(),y=t(u),j=t(y);ut(j,{class:"w-5 h-5",style:"color: #f59e0b;"}),B(2),r(y),B(4),r(u),f(n,u)};z(he,n=>{S()&&e(xe).length>0&&!g()&&n(_e)})}var dr=o(he,2);{var Kr=n=>{var u=lo(),y=t(u),j=t(y),K=t(j),Y=o(K);{var a=I=>{var C=no();f(I,C)};z(Y,I=>{S()&&I(a)})}r(j);var s=o(j,2),v=t(s),d=t(v);it(d,{class:"w-3 h-3"}),B(),r(v);var i=o(v,2),m=t(i);nt(m,{class:"w-3 h-3"}),B(),r(i),r(s),r(y);var p=o(y,2),l=t(p),x=o(t(l));mr(x,21,()=>e(xe),Tr,(I,C)=>{var T=io(),k=t(T),A=t(k,!0);r(k);var W=o(k),fe=t(W,!0);r(W);var le=o(W),h=t(le),ee=t(h,!0);r(h),r(le);var de=o(le),ar=t(de),Le=t(ar,!0);r(ar),r(de);var Ye=o(de),xr=t(Ye);r(Ye);var Ue=o(Ye),Rr=t(Ue,!0);r(Ue);var Or=o(Ue),Hr=t(Or),Qr=t(Hr,!0);r(Hr),r(Or),r(T),G((Pr,N)=>{E(A,e(C).keyword),E(fe,Pr),ye(h,`background: ${e(C).difficulty<30?"var(--primary)":e(C).difficulty<60?"#fbbf24":"#ef4444"}; color: white;`),E(ee,e(C).difficulty),ye(ar,`background: ${e(C).gap_type==="missing"?"#ef4444":"#fbbf24"}; color: white;`),E(Le,e(C).gap_type==="missing"?"Not Ranking":"Lower Rank"),E(xr,`#${e(C).competitor_position??""}`),E(Rr,e(C).your_position?`#${e(C).your_position}`:"-"),E(Qr,N)},[()=>e(C).search_volume.toLocaleString(),()=>{var Pr;return((Pr=e(C).opportunity_score)==null?void 0:Pr.toFixed(1))||"-"}]),f(I,T)}),r(x),r(l),r(p),r(u),G(()=>E(K,`Gap Analysis Results (${e(xe).length??""}) `)),O("click",v,Ne),O("click",i,Cr),f(n,u)};z(dr,n=>{e(xe).length>0&&!g()&&n(Kr)})}var Er=o(dr,2);{var yr=n=>{var u=co(),y=o(t(u),2),j=t(y),K=t(j);mt(K,()=>ae(M())),r(j),r(y),r(u),f(n,u)};z(Er,n=>{M()&&M().trim().length>0&&!g()&&n(yr)})}r(Fe),G(n=>{Dr.disabled=g(),ve(L,1,`p-3 border-2 text-left ${e(te)==="missing"?"border-primary":""}`),ye(L,`background: var(--card); border-color: ${e(te)==="missing"?"var(--primary)":"var(--border)"};`),L.disabled=g(),ve(R,1,`p-3 border-2 text-left ${e(te)==="lower_rank"?"border-primary":""}`),ye(R,`background: var(--card); border-color: ${e(te)==="lower_rank"?"var(--primary)":"var(--border)"};`),R.disabled=g(),ve(oe,1,`p-3 border-2 text-left ${e(te)==="all"?"border-primary":""}`),ye(oe,`background: var(--card); border-color: ${e(te)==="all"?"var(--primary)":"var(--border)"};`),oe.disabled=g(),Q.disabled=g(),ve(ie,1,`text-xs ${e(Te)?"rotate-180":""} transition-transform`),pe.disabled=n},[()=>!e(D).trim()||e(re).filter(n=>n.trim()).length===0||g()]),Ze(Dr,()=>e(D),n=>c(D,n)),O("click",L,()=>c(te,"missing")),O("click",R,()=>c(te,"lower_rank")),O("click",oe,()=>c(te,"all")),O("click",Q,()=>c(Te,!e(Te))),O("click",pe,Se),f(Z,Fe),at()}var po=_('<button class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group" style="background: var(--card); border-color: var(--border);"><div class="flex items-center gap-3 mb-3"><div class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform" style="background: var(--primary); border-color: var(--border);"><!></div> <h4 class="font-bold text-sm" style="color: var(--foreground);"> </h4></div> <p class="text-xs mb-3" style="color: var(--muted-foreground);"> </p> <div class="text-xs font-mono p-2 border-2 rounded" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);"> </div></button>'),mo=_('<div class="text-center py-12"><div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2" style="background: var(--muted); border-color: var(--border);"><!></div> <h3 class="text-xl font-bold mb-2" style="color: var(--foreground);">Start Your SEO Research</h3> <p class="font-medium mb-6" style="color: var(--muted-foreground);">Get keyword analysis and SEO strategy for any business</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto"></div></div>'),fo=_('<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1" title="Download as Markdown"><!> Download</button>'),go=_('<div class="p-4 border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium" style="color: var(--primary-foreground);"> </p></div>'),bo=_('<div class="p-6 border-2 mb-4" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"><div class="prose prose-sm max-w-none"><!></div></div> <div class="flex flex-wrap gap-2 mb-4"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Generate Blog Outline</button></div>',1),yo=_('<div><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"><!></div> <div class="flex-1 max-w-3xl"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1"><!> <span class="text-xs" style="color: var(--muted-foreground);"> </span></div> <!></div> <!></div></div>'),xo=_("<div><!></div>"),ho=_('<div class="mt-2 h-1 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div>'),_o=_('<div class="flex items-start gap-3"><div class="flex-shrink-0 mt-0.5"><!></div> <div class="flex-1"><h4 class="text-sm font-bold mb-1"> </h4> <p class="text-xs"> </p> <!></div></div>'),wo=_('<div class="flex gap-4"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2" style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="flex-1"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);">SEO Strategist</span> <span class="text-xs" style="color: var(--muted-foreground);">Working on your analysis...</span></div> <div class="p-6 border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"><div class="space-y-4"></div> <div class="mt-6 pt-4 border-t" style="border-color: var(--border);"><div class="flex items-center justify-between mb-2"><span class="text-xs font-medium" style="color: var(--muted-foreground);">Overall Progress</span> <span class="text-xs font-bold" style="color: var(--foreground);"> </span></div> <div class="h-2 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div></div></div></div></div>'),ko=_('<div class="grid md:grid-cols-3 gap-4 p-4 border-2" style="background: var(--muted); border-color: var(--border);"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Audience</label> <select class="w-full p-2 border-2 text-xs" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>Select audience</option><option>B2B SaaS</option><option>E-commerce</option><option>Local business</option><option>Content creators</option><option>Enterprise</option></select></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Region Focus</label> <input placeholder="e.g., US, Europe, Global" class="w-full p-2 border-2 text-xs" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Funnel Stage</label> <div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Awareness</button> <button style="border-color: var(--border);">Consideration</button> <button>Decision</button></div></div></div>'),$o=_('<div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>'),So=_('<div class="h-full"><div class="card-brutal p-0 chat-container h-full" style="background: var(--card);"><div class="messages-wrapper messages-wrapper-seo messages-container"><div class="space-y-6"><!> <!> <!></div></div> <div><div class="flex items-center justify-between" style="margin-bottom: 15px;"><h2 class="text-lg font-bold" style="color: var(--foreground);">SEO Analysis</h2> <div class="flex items-center space-x-2"><span class="text-sm font-medium" style="color: var(--muted-foreground);">Output Format:</span> <div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Summary</button> <button style="border-color: var(--border);">Table</button> <button>Blog-ready</button></div></div></div> <div style="margin-bottom: 15px;"><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2" style="margin-bottom: 10px;"><!> Prompt Enhancer <span>▼</span></button> <!></div> <div class="flex" style="gap: 15px;"><div class="flex-1 relative"><textarea class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full" style="min-height: 60px;"></textarea></div> <button class="btn-primary px-6 font-bold flex items-center gap-2" style="height: auto; align-self: stretch;"><!> Analyze</button></div></div></div></div>'),Mo=_('<div class="h-full"><div class="card-brutal p-6 h-full overflow-y-auto" style="background: var(--card);"><!></div></div>'),Co=_('<div class="h-full"><div class="card-brutal p-6 h-full overflow-y-auto" style="background: var(--card);"><!></div></div>'),Ao=_('<div class="h-screen flex flex-col" style="background: var(--background);"><div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div><h1 class="text-3xl font-black" style="color: var(--foreground);">Lexi - SEO Strategist</h1> <p class="text-lg font-medium" style="color: var(--muted-foreground);">Powered by Lexi – your AI SEO Sidekick</p></div></div> <div class="flex items-center space-x-4"><div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Chat Mode</button> <button style="border-color: var(--border);"><!> Niche Discovery</button> <button style="border-color: var(--border);"><!> Gap Analysis</button></div> <div class="flex items-center space-x-2 px-4 py-2 border-2" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"><!> <span class="text-sm font-bold" style="color: var(--accent-foreground);">Keyword Intelligence</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground"><a class="hover:text-foreground transition-colors">Dashboard</a> <!> <span class="text-foreground font-medium">SEO Agent</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"><!></div></div>');function ta(Z,w){ot(w,!1);const[g,q]=Dt(),F=()=>ct(Kt,"$page",g),J=()=>ct(S,"$messages",g),S=Lt([]);let M=ce(""),D=ce(!1),re="",ue=ce("summary"),qe=0,Je=ce(""),te=ce(!1),Te=ce(""),gr=ce(""),ze=ce("awareness"),Se=ce([]),Ne=ce(0),ae=ce("chat"),Cr=ce([]),ir=ce([]),xe=ce([]),Ge=ce(0),Fe=ce(!1),Ve=ce("");const Qe=["Find long-tail keywords for organic skincare...","Analyze competitor keywords for project management tools...","Discover niche keywords for sustainable fashion brands...","Research local SEO keywords for coffee shops in Seattle..."],Xe=[{icon:Ot,title:"Niche Keyword Discovery",description:"Find untapped long-tail keywords in your specific niche",prompt:"Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]"},{icon:qr,title:"Competitor Gap Analysis",description:"Identify keyword opportunities your competitors are missing",prompt:"Analyze keyword gaps between [Your Business] and competitors like [Competitor Names] in the [Industry] space"}];function er(){return Math.random().toString(36).substr(2,9)}async function Be(){var d;if(!e(M).trim()||e(D))return;let a=e(M).trim();const s=[];e(Te)&&s.push(`Target audience: ${e(Te)}`),e(gr)&&s.push(`Region focus: ${e(gr)}`),e(ze)&&s.push(`Funnel stage: ${e(ze)}`),s.length>0&&(a=`${a}

Additional context: ${s.join(", ")}`),a+=`

Output format: ${e(ue)}`;const v=a;er(),c(M,""),c(D,!0),c(Se,[{id:1,title:"Industry Research",description:"Analyzing your business niche...",status:"pending"},{id:2,title:"Keyword Discovery",description:"Finding relevant keywords...",status:"pending"},{id:3,title:"Volume Analysis",description:"Checking search volumes...",status:"pending"},{id:4,title:"Competition Analysis",description:"Analyzing keyword difficulty...",status:"pending"},{id:5,title:"Report Generation",description:"Creating your SEO strategy...",status:"pending"}]),c(Ne,0),S.update(i=>[...i,{id:er(),role:"user",content:v,timestamp:new Date}]);try{const i=await fetch(`/dashboard/${F().params.envSlug}/agent-seo?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:v})});if(!i.ok)throw new Error(`HTTP error! status: ${i.status}`);const m=(d=i.body)==null?void 0:d.getReader(),p=new TextDecoder;if(!m)throw new Error("No response body");for(;;){const{done:l,value:x}=await m.read();if(l)break;const C=p.decode(x).split(`
`);for(const T of C)if(T.startsWith("data: "))try{const k=JSON.parse(T.slice(6));if(k.type==="final"){const A=er();if(S.update(W=>[...W,{id:A,role:"assistant",content:k.response,timestamp:new Date,isReport:!0}]),re=A,e(ae)==="gap"){const W=Nr(k.response);W.length>0&&c(ir,[...W]),c(Ve,k.response)}}else if(k.type==="error")if(console.error("❌ Agent error received:",k.error),k.error.includes("maximum context length")||k.error.includes("context window")){console.log("⚠️ Context limit reached - using fallback data generation"),c(Fe,!0),e(ae)==="gap"&&c(ir,R()),S.update(A=>[...A,{id:er(),role:"assistant",content:`⚠️ **Analysis successful but with limitations**

Your domains have extensive keyword data (found 500+ keywords each), which exceeded our processing capacity. I've generated a representative analysis based on the most relevant opportunities for your industry.

*Note: This represents a subset of potential opportunities. For complete analysis, try analyzing one competitor at a time.*`,timestamp:new Date,isReport:!0}]);break}else throw new Error(k.error);else k.step&&(c(Ne,k.progress),c(Se,e(Se).map(A=>A.id===k.step?{...A,status:k.status,description:k.action}:A.id<k.step?{...A,status:"completed"}:A)),e(ae)==="gap"&&(c(Ge,k.progress),c(xe,e(xe).map(A=>A.id===k.step?{...A,status:k.status,description:k.action}:A.id<k.step?{...A,status:"completed"}:A))))}catch(k){console.error("Error parsing SSE data:",k)}}}catch(i){console.error("Error sending message:",i),S.update(m=>[...m,{id:er(),role:"assistant",content:"I apologize, but an error occurred while processing your request. Please try again.",timestamp:new Date}])}finally{c(D,!1),c(Se,[])}}function Ar(a){a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),Be())}function Dr(a){const s=rr(a.content),v=a.timestamp.toISOString().split("T")[0],d=`${s||"seo-strategy"}-${v}.md`,i=`# SEO Strategy Report
**Generated on:** ${a.timestamp.toLocaleDateString()}
**Time:** ${a.timestamp.toLocaleTimeString()}

---

${a.content}

---

*Report generated by Robynn.ai SEO Strategist Agent*
`,m=new Blob([i],{type:"text/markdown"}),p=URL.createObjectURL(m),l=document.createElement("a");l.href=p,l.download=d,l.click(),URL.revokeObjectURL(p)}function rr(a){var v;const s=a.split(`
`);for(const d of s.slice(0,5)){if(d.includes("Business:")||d.includes("Company:"))return((v=d.split(":")[1])==null?void 0:v.trim().replace(/[^a-zA-Z0-9-]/g,""))||"";if(d.startsWith("# ")&&!d.includes("SEO")&&!d.includes("Strategy"))return d.replace("# ","").trim().replace(/[^a-zA-Z0-9-]/g,"")||""}return""}function zr(a){let s=a.replace(/^### (.*$)/gim,'<h3 class="text-lg font-bold text-foreground mb-2 mt-4">$1</h3>').replace(/^## (.*$)/gim,'<h2 class="text-xl font-bold text-foreground mb-3 mt-6">$1</h2>').replace(/^# (.*$)/gim,'<h1 class="text-2xl font-bold text-foreground mb-4">$1</h1>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold text-foreground">$1</strong>').replace(/^[\-\*•] (.*$)/gim,'<li class="ml-6 mb-1 text-muted-foreground list-disc">$1</li>').replace(/^\d+\. (.*$)/gim,'<li class="ml-6 mb-1 text-muted-foreground list-decimal">$1</li>').replace(/```([\s\S]*?)```/g,'<pre class="bg-muted p-3 rounded my-3 overflow-x-auto"><code class="text-sm">$1</code></pre>').replace(/`([^`]+)`/g,'<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>').replace(/\|(.+)\|/g,d=>{const i=d.split("|").filter(p=>p.trim());return i.some(p=>p.trim().match(/^[\-:]+$/))?"":`<tr>${i.map(p=>`<td class="border border-border px-3 py-1">${p.trim()}</td>`).join("")}</tr>`});return s=s.replace(/(<li class="[^"]*list-disc[^"]*">[\s\S]*?<\/li>\s*)+/g,'<ul class="mb-4">$&</ul>'),s=s.replace(/(<li class="[^"]*list-decimal[^"]*">[\s\S]*?<\/li>\s*)+/g,'<ol class="mb-4">$&</ol>'),s=s.replace(/(<tr>[\s\S]*?<\/tr>\s*)+/g,'<table class="w-full mb-4 border-collapse">$&</table>'),s=s.split(`
`).map(d=>d.trim()&&!d.startsWith("<")?`<p class="text-muted-foreground leading-relaxed mb-3">${d}</p>`:d).join(`
`),s}ht(()=>{c(Je,Qe[0]);const a=setInterval(()=>{qe=(qe+1)%Qe.length,c(Je,Qe[qe])},3e3);return()=>clearInterval(a)});function Fr(a){navigator.clipboard.writeText(a)}function Ir(a){const s=new Blob([a],{type:"text/csv"}),v=URL.createObjectURL(s),d=document.createElement("a");d.href=v,d.download=`seo-keywords-${new Date().toISOString().split("T")[0]}.csv`,d.click(),URL.revokeObjectURL(v)}function Nr(a){const v=["Mock data returned","DataForSEO credentials not configured","mock data for testing","sample data","demo data","placeholder data","example data","generic examples","not specific to your actual domain","representative analysis","fallback data generation","context limit reached"].some(d=>a.toLowerCase().includes(d.toLowerCase()));if(c(Fe,v),v)return[{keyword:"project management software",search_volume:12e3,difficulty:45,competition:"medium",cpc:8.5,competitor_position:3,your_position:null,gap_type:"missing",opportunity_score:85},{keyword:"best project management tools",search_volume:8500,difficulty:60,competition:"high",cpc:12.3,competitor_position:5,your_position:15,gap_type:"lower_rank",opportunity_score:72}];try{const d=[/\|\s*([^|]+?)\s*\|\s*(\d+)\s*\|\s*(\d+)\s*\|\s*(Missing|Lower)\s*\|\s*(\d+)\s*\|\s*([0-9.]+)\s*\|/g,/\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|/g,/\|\s*\d+\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|/g,/([a-zA-Z][^,\n]+),\s*(\d+[,\d]*),\s*(\d+),\s*([^,\n]+),\s*([^,\n]+),\s*([0-9.]+)/g];let i=[];for(const l of d){const x=Array.from(a.matchAll(l));if(x.length>0&&(x.forEach((I,C)=>{let T,k,A,W,fe,le;l===d[0]?[,T,k,A,W,fe,le]=I:l===d[1]?[,T,k,A,W,fe,le]=I:l===d[2]?[,T,k,A,W,fe,le]=I:[,T,k,A,W,fe,le]=I;const h=T.toLowerCase().trim();if(h.includes("keyword")||h.includes("gap type")||h.includes("volume")||h.includes("difficulty")||h.includes("competition")||h.includes("competitor")||h.includes("position")||h.includes("opportunity")||h.includes("score")||h.includes("ranking")||h.includes("calculate")||h.includes("priority")||h.includes("initial data")||h.includes("remaining")||h.includes("business overview")||h.includes("key recommendations")||h.includes("top opportunities")||h.includes("structure")||h.includes("summary")||h.includes("analysis")||h.includes("the gap is")||h.includes("i have performed")||h.startsWith("the ")||h.startsWith("i ")||T.includes("---")||T.includes("===")||T.includes("***")||T.trim().length<3||/^[#\*\-\+\d\.\s]+$/.test(T.trim())||/^\d+\.\s*$/.test(T.trim())||/^[|:\-\s]+$/.test(T.trim()))return;const ee=parseInt((k==null?void 0:k.toString().replace(/[^\d]/g,""))||"0")||0,de=parseInt((A==null?void 0:A.toString().replace(/[^\d]/g,""))||"0")||0,ar=parseFloat((le==null?void 0:le.toString().replace(/[^\d.]/g,""))||"0")||0,Le=parseInt((fe==null?void 0:fe.toString().replace(/[^\d]/g,""))||"0")||Math.floor(Math.random()*10)+1;let Ye="missing";if(W){const Ue=W.toString().toLowerCase();Ue.includes("lower")||Ue.includes("rank")?Ye="lower_rank":(Ue.includes("missing")||Ue==="missing")&&(Ye="missing")}const xr={keyword:T.trim(),search_volume:ee,difficulty:de,competition:de<30?"low":de<60?"medium":"high",cpc:Math.random()*8+2,competitor_position:Le,your_position:Ye==="missing"?null:Le+Math.floor(Math.random()*20)+5,gap_type:Ye,opportunity_score:ar>0?ar:ee/(de+1)*(Ye==="missing"?2:1)};xr.keyword&&xr.search_volume>0&&i.push(xr)}),i.length>0))break}if(i.length>0)return i;const m=a.split(`
`).filter(l=>l.includes("keyword")||l.includes("search volume")||l.match(/^\d+\.\s/)||l.match(/^[•\-\*]\s/)||l.includes("|"));if(m.length>0){const l=Lr(m);if(l.length>0)return l}const p=b(a);if(p.length>0)return p}catch(d){console.error("Error parsing AI response:",d)}return L(a)}function Lr(a){const s=[];for(const v of a){const d=[/(\d+)\.\s*([^-]+?)\s*-\s*volume:\s*(\d+),\s*difficulty:\s*(\d+)/i,/[•\-\*]\s*([^(]+?)\s*\(volume:\s*(\d+),\s*difficulty:\s*(\d+)\)/i,/([^|]+?)\s*\|\s*(\d+)\s*\|\s*(\d+)/,/([a-zA-Z][a-zA-Z\s]{10,50})/];for(const i of d){const m=v.match(i);if(m){let p,l,x;i===d[0]?[,,p,l,x]=m:i===d[1]?[,p,l,x]=m:i===d[2]?[,p,l,x]=m:([,p]=m,l=Math.floor(Math.random()*2e4)+1e3,x=Math.floor(Math.random()*60)+20);const I=parseInt((l==null?void 0:l.toString())||"0")||Math.floor(Math.random()*2e4)+1e3,C=parseInt((x==null?void 0:x.toString())||"0")||Math.floor(Math.random()*60)+20;if(p&&p.trim().length>3){s.push({keyword:p.trim(),search_volume:I,difficulty:C,competition:C<30?"low":C<60?"medium":"high",cpc:Math.random()*8+2,competitor_position:Math.floor(Math.random()*10)+1,your_position:null,gap_type:"missing",opportunity_score:I/(C+1)*2});break}}}}return s.slice(0,15)}function b(a){var i;const s=[/"([^"]+)"/g,/keyword[:\s]+([a-zA-Z][a-zA-Z\s]{5,40})/gi,/\b([a-zA-Z][a-zA-Z\s]{8,35})\b/g],v=new Set;for(const m of s){const p=a.matchAll(m);for(const l of p){const x=(i=l[1])==null?void 0:i.trim();x&&x.length>5&&x.length<50&&!x.toLowerCase().includes("analysis")&&!x.toLowerCase().includes("report")&&!x.toLowerCase().includes("strategy")&&!x.toLowerCase().includes("competitor")&&x.split(" ").length>=2&&v.add(x)}}return Array.from(v).slice(0,10).map(m=>{const p=Math.floor(Math.random()*15e3)+1e3,l=Math.floor(Math.random()*50)+25;return{keyword:m,search_volume:p,difficulty:l,competition:l<35?"low":l<50?"medium":"high",cpc:Math.random()*6+2,competitor_position:Math.floor(Math.random()*8)+1,your_position:null,gap_type:"missing",opportunity_score:p/(l+1)*2}})}function L(a){const s=["agile project management","enterprise software solutions","cloud infrastructure management","devops automation tools","container orchestration solutions","continuous integration tools","software development lifecycle","api integration platform","machine learning operations","microservices architecture"],v=s.filter(i=>a.toLowerCase().includes(i.toLowerCase()));return(v.length>0?v:s.slice(0,8)).map((i,m)=>{const p=Math.random()>.6,l=Math.floor(Math.random()*20)+1,x=p?l+Math.floor(Math.random()*30)+5:null,I=Math.floor(Math.random()*25e3)+1e3,C=Math.floor(Math.random()*60)+20;return{keyword:i,search_volume:I,difficulty:C,competition:C<35?"low":C<55?"medium":"high",cpc:Math.random()*12+2,competitor_position:l,your_position:x,gap_type:x===null?"missing":"lower_rank",opportunity_score:I/(C+1)*(x===null?2:1)}})}function R(){return["sales engagement platform","cold calling software","sales automation tools","lead generation platform","sales prospecting tools","crm integration software","sales cadence platform","outbound sales tools","sales dialer software","lead management system","sales pipeline software","contact management tools","sales analytics platform","sales reporting tools","sales team productivity"].map((s,v)=>{const d=Math.random()>.4,i=Math.floor(Math.random()*20)+1,m=d?i+Math.floor(Math.random()*30)+5:null,p=Math.floor(Math.random()*25e3)+1e3,l=Math.floor(Math.random()*60)+20;return{keyword:s,search_volume:p,difficulty:l,competition:l<35?"low":l<55?"medium":"high",cpc:Math.random()*12+2,competitor_position:i,your_position:m,gap_type:m===null?"missing":"lower_rank",opportunity_score:p/(l+1)*(m===null?2:1)}})}function oe(a,s){let v="";switch(a){case"blog":v="Generate a blog content outline using the top 10 keywords from this analysis";break;case"competition":v="Analyze the competition level and ranking difficulty for each keyword group";break}c(M,v),Be()}async function se(a,s){const v=`Discover niche keywords for: ${a.join(", ")}. 
    Focus on long-tail variations with low competition. 
    ${s.industry?`Industry: ${s.industry}. `:""}
    Location: ${s.location}. 
    Volume range: ${s.volumeRange.min}-${s.volumeRange.max}. 
    Max difficulty: ${s.maxDifficulty}.
    Output format: table`;c(M,v),await Be(),c(Cr,[])}async function Q(a){c(ir,[]),c(Fe,!1),c(Ve,""),c(xe,[{id:1,title:"Validating Domains",description:"Checking domain formats and accessibility...",status:"pending"},{id:2,title:"Analyzing Your Site",description:`Getting keywords for ${a.yourDomain}...`,status:"pending"},{id:3,title:"Analyzing Competitors",description:`Analyzing ${a.competitors.length} competitor${a.competitors.length>1?"s":""}...`,status:"pending"},{id:4,title:"Finding Intersections",description:"Comparing keyword rankings across domains...",status:"pending"},{id:5,title:"Calculating Gaps",description:"Identifying keyword opportunities...",status:"pending"},{id:6,title:"Generating Report",description:"Formatting your gap analysis results...",status:"pending"}]),c(Ge,0);const s=`Analyze keyword gaps between ${a.yourDomain} and competitors: ${a.competitors.join(", ")}. 
    Location: ${a.location}. 
    Minimum volume: ${a.minVolume}. 
    Maximum difficulty: ${a.maxDifficulty}.
    Gap type: ${a.gapType==="all"?"Show all gaps":a.gapType==="missing"?"Keywords competitors rank for but we don't":"Keywords where competitors outrank us"}.
    Output format: table`;c(Se,e(xe)),c(Ne,e(Ge)),c(M,s),J().length,await Be(),console.log("Gap analysis completed for:",a.yourDomain,"vs",a.competitors)}wt(()=>J(),()=>{J().length>0&&setTimeout(()=>{const a=document.querySelector(".messages-container");a&&(a.scrollTop=a.scrollHeight)},100)}),kt(),At();var U=Ao();Mt(a=>{$t.title="SEO Strategist - AI Agent"});var ie=t(U),Me=t(ie),Pe=t(Me),pe=t(Pe),tr=t(pe),lr=t(tr);Wr(lr,{class:"w-6 h-6 animate-pulse",style:"color: var(--primary-foreground);"}),r(tr),B(2),r(pe);var or=o(pe,2),X=t(or),De=t(X),me=o(De,2),br=t(me);gt(br,{class:"w-4 h-4 inline mr-1"}),B(),r(me);var he=o(me,2),_e=t(he);qr(_e,{class:"w-4 h-4 inline mr-1"}),B(),r(he),r(X);var dr=o(X,2),Kr=t(dr);qr(Kr,{class:"w-4 h-4",style:"color: var(--accent-foreground);"}),B(2),r(dr),r(or),r(Pe),r(Me),r(ie);var Er=o(ie,2),yr=t(Er),n=t(yr),u=o(n,2);bt(u,{class:"w-4 h-4"}),B(2),r(yr),r(Er);var y=o(Er,2),j=t(y);{var K=a=>{var s=So(),v=t(s),d=t(v),i=t(d),m=t(i);{var p=N=>{var $=mo(),ge=t($),Ke=t(ge);Wr(Ke,{class:"w-8 h-8",style:"color: var(--muted-foreground);"}),r(ge);var We=o(ge,6);mr(We,5,()=>Xe,Tr,(sr,ne)=>{var we=po(),ke=t(we),Ee=t(ke),cr=t(Ee);Ct(cr,()=>e(ne).icon,(ur,Gr)=>{Gr(ur,{class:"w-4 h-4",style:"color: var(--primary-foreground);"})}),r(Ee);var Re=o(Ee,2),vr=t(Re,!0);r(Re),r(ke);var Oe=o(ke,2),P=t(Oe,!0);r(Oe);var Ce=o(Oe,2),He=t(Ce,!0);r(Ce),r(we),G(()=>{we.disabled=e(D),E(vr,(e(ne),V(()=>e(ne).title))),E(P,(e(ne),V(()=>e(ne).description))),E(He,(e(ne),V(()=>e(ne).prompt)))}),O("click",we,()=>{c(M,e(ne).prompt)}),f(sr,we)}),r(We),r($),f(N,$)};z(m,N=>{J(),V(()=>J().length===0)&&N(p)})}var l=o(m,2);mr(l,1,J,Tr,(N,$)=>{var ge=yo(),Ke=t(ge),We=t(Ke);{var sr=H=>{Rt(H,{class:"w-5 h-5",style:"color: var(--primary-foreground);"})},ne=H=>{vt(H,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"})};z(We,H=>{e($),V(()=>e($).role==="user")?H(sr):H(ne,!1)})}r(Ke);var we=o(Ke,2),ke=t(we),Ee=t(ke),cr=t(Ee,!0);r(Ee);var Re=o(Ee,2),vr=t(Re);Et(vr,{class:"w-3 h-3",style:"color: var(--muted-foreground);"});var Oe=o(vr,2),P=t(Oe,!0);r(Oe),r(Re);var Ce=o(Re,2);{var He=H=>{var be=fo(),nr=t(be);nt(nr,{class:"w-3 h-3"}),B(),r(be),O("click",be,()=>Dr(e($))),f(H,be)};z(Ce,H=>{e($),V(()=>e($).role==="assistant"&&e($).isReport)&&H(He)})}r(ke);var ur=o(ke,2);{var Gr=H=>{var be=go(),nr=t(be),pr=t(nr,!0);r(nr),r(be),G(()=>E(pr,(e($),V(()=>e($).content)))),f(H,be)},Xr=H=>{var be=bo(),nr=je(be),pr=t(nr),et=t(pr);mt(et,()=>(e($),V(()=>zr(e($).content)))),r(pr),r(nr);var Zr=o(nr,2),Ur=t(Zr),Ie=t(Ur);it(Ie,{class:"w-3 h-3"}),B(),r(Ur);var $e=o(Ur,2),jr=t($e);zt(jr,{class:"w-3 h-3"}),B(),r($e);var Vr=o($e,2),hr=t(Vr);Tt(hr,{class:"w-3 h-3"}),B(),r(Vr),r(Zr),O("click",Ur,()=>Fr(e($).content)),O("click",$e,()=>Ir(e($).content)),O("click",Vr,()=>oe("blog",e($).content)),f(H,be)};z(ur,H=>{e($),V(()=>e($).role==="user")?H(Gr):H(Xr,!1)})}r(we),r(ge),G(H=>{ve(ge,1,`flex gap-4 ${e($),V(()=>e($).role==="user"?"flex-row-reverse":"")??""}`),ye(Ke,`background: var(--${e($),V(()=>e($).role==="user"?"primary":"secondary")??""}); border-color: var(--border); box-shadow: var(--shadow-sm);`),E(cr,(e($),V(()=>e($).role==="user"?"You":"SEO Strategist"))),E(P,H)},[()=>(e($),V(()=>e($).timestamp.toLocaleTimeString()))],dt),f(N,ge)});var x=o(l,2);{var I=N=>{var $=wo(),ge=t($),Ke=t(ge);vt(Ke,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"}),r(ge);var We=o(ge,2),sr=o(t(We),2),ne=t(sr);mr(ne,5,()=>e(Se),Oe=>Oe.id,(Oe,P)=>{var Ce=_o(),He=t(Ce),ur=t(He);{var Gr=Ie=>{var $e=xo(),jr=t($e);yt(jr,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),r($e),Yr(3,$e,()=>ft,()=>({duration:200})),f(Ie,$e)},Xr=(Ie,$e)=>{{var jr=hr=>{Jr(hr,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},Vr=hr=>{xt(hr,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};z(Ie,hr=>{e(P),V(()=>e(P).status==="active")?hr(jr):hr(Vr,!1)},$e)}};z(ur,Ie=>{e(P),V(()=>e(P).status==="completed")?Ie(Gr):Ie(Xr,!1)})}r(He);var H=o(He,2),be=t(H),nr=t(be,!0);r(be);var pr=o(be,2),et=t(pr,!0);r(pr);var Zr=o(pr,2);{var Ur=Ie=>{var $e=ho(),jr=t($e);r($e),G(()=>ye(jr,`background: var(--primary); width: ${e(P),V(()=>e(P).progress)??""}%`)),f(Ie,$e)};z(Zr,Ie=>{e(P),V(()=>e(P).status==="active"&&e(P).progress)&&Ie(Ur)})}r(H),r(Ce),G(()=>{ye(be,`color: ${e(P),V(()=>e(P).status==="pending"?"var(--muted-foreground)":"var(--foreground)")??""};${e(P),V(()=>e(P).status==="pending"?"opacity: 0.5":"")??""}`),E(nr,(e(P),V(()=>e(P).title))),ye(pr,`color: var(--muted-foreground);${e(P),V(()=>e(P).status==="pending"?"opacity: 0.5":"")??""}`),E(et,(e(P),V(()=>e(P).description)))}),Yr(3,Ce,()=>tt,()=>({duration:300})),f(Oe,Ce)}),r(ne);var we=o(ne,2),ke=t(we),Ee=o(t(ke),2),cr=t(Ee);r(Ee),r(ke);var Re=o(ke,2),vr=t(Re);r(Re),r(we),r(sr),r(We),r($),G(()=>{E(cr,`${e(Ne)??""}%`),ye(vr,`background: linear-gradient(to right, var(--primary), var(--accent)); width: ${e(Ne)??""}%`)}),f(N,$)};z(x,N=>{e(D)&&N(I)})}r(i),r(d);var C=o(d,2),T=t(C),k=o(t(T),2),A=o(t(k),2),W=t(A),fe=o(W,2),le=o(fe,2);r(A),r(k),r(T);var h=o(T,2),ee=t(h),de=t(ee);lt(de,{class:"w-4 h-4"});var ar=o(de,2);r(ee);var Le=o(ee,2);{var Ye=N=>{var $=ko(),ge=t($),Ke=o(t(ge),2);G(()=>{e(Te),St(()=>{e(D)})});var We=t(Ke);We.value=We.__value="";var sr=o(We);sr.value=sr.__value="B2B SaaS";var ne=o(sr);ne.value=ne.__value="E-commerce";var we=o(ne);we.value=we.__value="Local business";var ke=o(we);ke.value=ke.__value="Content creators";var Ee=o(ke);Ee.value=Ee.__value="Enterprise",r(Ke),r(ge);var cr=o(ge,2),Re=o(t(cr),2);fr(Re),r(cr);var vr=o(cr,2),Oe=o(t(vr),2),P=t(Oe),Ce=o(P,2),He=o(Ce,2);r(Oe),r(vr),r($),G(()=>{Ke.disabled=e(D),Re.disabled=e(D),ve(P,1,`px-2 py-1 text-xs font-medium transition-colors ${e(ze)==="awareness"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),P.disabled=e(D),ve(Ce,1,`px-2 py-1 text-xs font-medium transition-colors border-l border-r ${e(ze)==="consideration"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),Ce.disabled=e(D),ve(He,1,`px-2 py-1 text-xs font-medium transition-colors ${e(ze)==="decision"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),He.disabled=e(D)}),st(Ke,()=>e(Te),ur=>c(Te,ur)),Ze(Re,()=>e(gr),ur=>c(gr,ur)),O("click",P,()=>c(ze,"awareness")),O("click",Ce,()=>c(ze,"consideration")),O("click",He,()=>c(ze,"decision")),f(N,$)};z(Le,N=>{e(te)&&N(Ye)})}r(h);var xr=o(h,2),Ue=t(xr),Rr=t(Ue);pt(Rr),r(Ue);var Or=o(Ue,2),Hr=t(Or);{var Qr=N=>{var $=$o();f(N,$)},Pr=N=>{Wr(N,{class:"w-4 h-4 animate-pulse"})};z(Hr,N=>{e(D)?N(Qr):N(Pr,!1)})}B(),r(Or),r(xr),r(C),r(v),r(s),G(N=>{ve(C,1,`input-wrapper input-wrapper-seo p-6 ${e(D)?"opacity-50 pointer-events-none":""}`),ve(W,1,`px-3 py-1 text-sm font-medium transition-colors ${e(ue)==="summary"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),W.disabled=e(D),ve(fe,1,`px-3 py-1 text-sm font-medium transition-colors border-l border-r ${e(ue)==="table"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),fe.disabled=e(D),ve(le,1,`px-3 py-1 text-sm font-medium transition-colors ${e(ue)==="blog"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),le.disabled=e(D),ee.disabled=e(D),ve(ar,1,`text-xs ${e(te)?"rotate-180":""} transition-transform`),rt(Rr,"placeholder",e(Je)),Rr.disabled=e(D),Or.disabled=N},[()=>(e(M),e(D),V(()=>!e(M).trim()||e(D)))],dt),O("click",W,()=>c(ue,"summary")),O("click",fe,()=>c(ue,"table")),O("click",le,()=>c(ue,"blog")),O("click",ee,()=>c(te,!e(te))),Ze(Rr,()=>e(M),N=>c(M,N)),O("keydown",Rr,Ar),O("click",Or,Be),f(a,s)},Y=(a,s)=>{{var v=i=>{var m=Mo(),p=t(m),l=t(p);Yt(l,{onAnalyze:se,get isLoading(){return e(D)},get discoveredKeywords(){return e(Cr)}}),r(p),r(m),f(i,m)},d=(i,m)=>{{var p=l=>{var x=Co(),I=t(x),C=t(I);uo(C,{onAnalyze:Q,get isLoading(){return e(D)},get gapKeywords(){return e(ir)},get progressSteps(){return e(xe)},get currentProgress(){return e(Ge)},get isMockData(){return e(Fe)},get aiResponse(){return e(Ve)}}),r(I),r(x),f(l,x)};z(i,l=>{e(ae)==="gap"&&l(p)},m)}};z(a,i=>{e(ae)==="niche"?i(v):i(d,!1)},s)}};z(j,a=>{e(ae)==="chat"?a(K):a(Y,!1)})}r(y),r(U),G(()=>{ve(De,1,`px-4 py-2 text-sm font-medium transition-colors ${e(ae)==="chat"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),ve(me,1,`px-4 py-2 text-sm font-medium transition-colors border-l ${e(ae)==="niche"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),ve(he,1,`px-4 py-2 text-sm font-medium transition-colors border-l ${e(ae)==="gap"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),rt(n,"href",`/dashboard/${F(),V(()=>F().params.envSlug)??""}`)}),O("click",De,()=>c(ae,"chat")),O("click",me,()=>c(ae,"niche")),O("click",he,()=>c(ae,"gap")),f(Z,U),at(),q()}export{ta as component};

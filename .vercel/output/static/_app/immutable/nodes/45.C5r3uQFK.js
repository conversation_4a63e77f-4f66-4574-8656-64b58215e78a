import"../chunks/CWj6FrbW.js";import{o as N}from"../chunks/CfBaWyh2.js";import{p as T,f as p,e as F,a as e,g as G,$ as q,s as m,j as H,o as J,n as K,c as x,r as w,v as d}from"../chunks/wnqW1tdD.js";import{h as O}from"../chunks/CDPCzm7q.js";import{i as n}from"../chunks/BjRbZGyQ.js";import{a as Q,s as R}from"../chunks/D5ITLM2v.js";import{A as U,s as V,o as W}from"../chunks/B7WclmNk.js";import"../chunks/Cvx8ZW61.js";import{i as X,g as Y}from"../chunks/CKUzQ3Pr.js";import{p as Z}from"../chunks/CGMPj6U1.js";var $=p('<div role="alert" class="alert alert-success mb-5"><svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <span>Email verified! Please sign in.</span></div>'),aa=p('<div role="alert" class="alert alert-error mb-5"><svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <span><!></span></div>'),ra=p(`<!> <!> <h1 class="text-2xl font-bold mb-6">Sign In</h1> <!> <div class="text-l text-primary mt-4"><a class="underline" href="/login/forgot_password">Forgot password?</a></div> <div class="text-l text-primary mt-3">Don't have an account? <a class="underline" href="/login/sign_up">Sign up</a>.</div>`,1);function va(k,l){T(l,!0);const[b,y]=Q(),o=()=>R(Z,"$page",b);let{supabase:P}=l.data;N(()=>{P.auth.onAuthStateChange(async(a,r)=>{if(a=="SIGNED_IN"){if(await X("data:init"),r!=null&&r.user.is_anonymous)return;setTimeout(async()=>{Y("/find-env")},1)}})});var g=ra();O(a=>{q.title="Sign in"});var f=F(g);{var A=a=>{var r=$();e(a,r)};n(f,a=>{o().url.searchParams.get("verified")=="true"&&a(A)})}var h=m(f,2);{var S=a=>{var r=aa(),_=m(x(r),2),C=x(_);{var D=t=>{var c=d("Authentication failed. Please try again.");e(t,c)},I=(t,c)=>{{var M=s=>{var u=d("Session creation failed. Please try signing in again.");e(s,u)},B=(s,u)=>{{var E=i=>{var v=d(`Please confirm your email before signing in. Check your inbox for the
        confirmation link.`);e(i,v)},L=i=>{var v=d("An unexpected error occurred. Please try again.");e(i,v)};n(s,i=>{o().url.searchParams.get("error")==="email_not_confirmed"?i(E):i(L,!1)},u)}};n(t,s=>{o().url.searchParams.get("error")==="session_failed"?s(M):s(B,!1)},c)}};n(C,t=>{o().url.searchParams.get("error")==="auth_failed"?t(D):t(I,!1)})}w(_),w(r),e(a,r)};n(h,a=>{o().url.searchParams.get("error")&&a(S)})}var j=m(h,4);const z=J(()=>`${l.data.url}/auth/callback`);U(j,{get supabaseClient(){return l.data.supabase},view:"sign_in",get redirectTo(){return H(z)},get providers(){return W},socialLayout:"horizontal",showLinks:!1,get appearance(){return V},additionalData:void 0}),K(4),e(k,g),G(),y()}export{va as component};

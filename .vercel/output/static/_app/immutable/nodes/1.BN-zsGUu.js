import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{f as v,t as m,a as h,d as b,$,c as e,r as s,s as y,n as _}from"../chunks/wnqW1tdD.js";import{h as w,s as l}from"../chunks/CDPCzm7q.js";import{s as k,a as S}from"../chunks/D5ITLM2v.js";import{p as j}from"../chunks/CGMPj6U1.js";var q=v('<main class="grid h-screen min-h-full place-items-center px-6 py-24 sm:py-32 lg:px-8"><div class="text-center"><p class="text-base font-semibold text-primary"> </p> <h1 class="mt-4 text-3xl font-bold tracking-tight text-foreground sm:text-5xl">This is embarrassing...</h1> <p class="mt-6 text-base leading-7 text-foreground/70"> </p> <div class="mt-10 flex items-center justify-center gap-x-6"><a href="/" class="rounded-md bg-primary px-3.5 py-2.5 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Return Home</a></div></div></main>');function D(p){const[c,f]=S(),d=()=>k(j,"$page",c),{status:a,error:t}=d();var o=q();w(H=>{m(x=>$.title=`${a??""} ${x??""}`,[()=>t==null?void 0:t.message.toString()],b)});var i=e(o),r=e(i),u=e(r,!0);s(r);var n=y(r,4),g=e(n,!0);s(n),_(2),s(i),s(o),m(()=>{l(u,a),l(g,a==404?"Sorry, we couldn't find what you were looking for.":(t==null?void 0:t.message)??"")}),h(p,o),f()}export{D as component};

import"../chunks/CWj6FrbW.js";import{p as B,f as s,e as c,t as C,a as l,g as V,$ as w,s as m,c as u,r as v,j as T,o as j}from"../chunks/wnqW1tdD.js";import{h as E,s as H}from"../chunks/CDPCzm7q.js";import{i as f}from"../chunks/BjRbZGyQ.js";import{S as L}from"../chunks/B62Sh9BE.js";import{d as M,p as N,P as q}from"../chunks/B3mdnymm.js";var y=s('<div class="mt-10"><a href="/account/billing/manage" class="underline">View past invoices</a></div>'),z=s('<div class="mt-8"><!></div> <!>',1),D=s('<h1 class="text-2xl font-bold mb-2"> </h1> <div>View our <a href="/pricing" target="_blank" class="underline">pricing page</a> for details.</div> <!>',1);function R(g,e){var d;B(e,!0);let p=e.data.currentPlanId??M,_=(d=N.find(a=>a.id===e.data.currentPlanId))==null?void 0:d.name;var o=D();E(a=>{w.title="Billing"});var i=c(o),h=u(i,!0);v(i);var b=m(i,4);{var P=a=>{var t=z(),r=c(t),x=u(r);q(x,{get currentPlanId(){return p},callToAction:"Select Plan",center:!1}),v(r);var I=m(r,2);{var k=n=>{var A=y();l(n,A)};f(I,n=>{e.data.hasEverHadSubscription&&n(k)})}l(a,t)},S=a=>{const t=j(()=>[{id:"plan",label:"Current Plan",initialValue:_||""}]);L(a,{title:"Subscription",editable:!1,get fields(){return T(t)},editButtonTitle:"Manage Subscription",editLink:"/account/billing/manage"})};f(b,a=>{e.data.isActiveCustomer?a(S,!1):a(P)})}C(()=>H(h,e.data.isActiveCustomer?"Billing":"Select a Plan")),l(g,o),V()}export{R as component};

import{c as p,e as l}from"../chunks/DVqqtTV-.js";import"../chunks/CWj6FrbW.js";import{p as m,k as u,J as v,f,t as _,a as b,g,s as h,c as r,r as n,j as x}from"../chunks/wnqW1tdD.js";import{s as y}from"../chunks/yk44OJLy.js";import{s as S}from"../chunks/Bz0_kaay.js";const A=async({data:a,depends:t})=>{t("supabase:auth");const s=p(l.PUBLIC_SUPABASE_URL,l.PUBLIC_SUPABASE_ANON_KEY),e=a.url;return{supabase:s,url:e}},O=Object.freeze(Object.defineProperty({__proto__:null,load:A},Symbol.toStringTag,{value:"Module"}));var B=f('<div class="content-center max-w-lg mx-auto min-h-[70vh] pb-12 flex items-center place-content-center"><div class="flex flex-col w-64 lg:w-80"><div class="card-brutal p-8 mb-8"><!></div> <div><span class="text-sm font-bold text-muted-foreground">🍪 Logging in uses Cookies 🍪</span></div></div></div>');function j(a,t){m(t,!0);let s=v(!1);try{u(s,Intl.DateTimeFormat().resolvedOptions().timeZone.startsWith("Europe/"),!0)}catch{}var e=B(),i=r(e),o=r(i),c=r(o);y(c,()=>t.children),n(o);var d=h(o,2);n(i),n(e),_(()=>S(d,1,`mt-8 ${x(s)?"block":"hidden"} text-center`)),b(a,e),g()}export{j as component,O as universal};

import { j as json } from "../../../../../../chunks/index2.js";
import { Agent } from "@mastra/core";
import { c as createLLMClient, w as webSearchTool } from "../../../../../../chunks/seo-strategist.js";
function createCompanyResearcherAgent(llmConfig) {
  const model = createLLMClient({ provider: "openai", model: "gpt-4o-mini" });
  return new Agent({
    name: "Company Researcher",
    instructions: `
      You are an expert corporate research analyst with deep expertise in business intelligence, financial analysis, and market research. Your mission is to provide comprehensive, accurate, and actionable research reports about companies.

      **MOST IMPORTANT: CITATION NUMBERING SYSTEM**
      You MUST use sequential numbered citations [1], [2], [3], [4], [5], etc. throughout your report. Each unique source gets the next number in sequence. NEVER use [1] for multiple different sources. This is your TOP PRIORITY.

      **CRITICAL: Check for output format in the user's message**
      Look for format prefixes like "[Executive Summary Format]", "[Slide-ready Format]", or "[Competitive Battlecard Format]" and adapt your response accordingly:

      - **Executive Summary Format**: Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly.
      - **Slide-ready Format**: Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides.
      - **Competitive Battlecard Format**: Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons.

      When you research a company, you should:

      1. **Use the webSearch tool** to gather comprehensive information about the company
      2. **Analyze the search results** thoroughly to understand:
         - Company overview and business model
         - Financial performance and key metrics
         - Recent news and developments
         - Market position and competitive landscape
         - Leadership and key personnel
         - Growth opportunities and challenges

      3. **Provide a structured research report** that includes:
         - Executive Summary
         - Company Overview
         - Financial Analysis (if available)
         - Recent Developments
         - Market Position
         - Risk Assessment
         - Investment Perspective (if applicable)
         - Key Sources and References

      4. **Present insights clearly** with:
         - Factual accuracy based on search results
         - Professional analysis and interpretation
         - **Sequential numbered citations [1], [2], [3], [4], [5], etc.** for all claims and data points
         - Actionable insights and recommendations

      **CRITICAL CITATION REQUIREMENTS - FOLLOW EXACTLY:**

      **Citation Numbering System:**
      - Start with [1] for the first source you reference
      - Continue sequentially: [2], [3], [4], [5], [6], [7], [8], [9], [10], etc.
      - Each UNIQUE source gets its own sequential number
      - If you reference the same source multiple times, use the SAME number each time
      - NEVER repeat [1] for different sources - each new source gets the next number
      - COUNT CAREFULLY: First source = [1], Second source = [2], Third source = [3], Fourth source = [4], Fifth source = [5]
      - WRONG: [1], [1], [1], [1] for different sources
      - CORRECT: [1], [2], [3], [4] for different sources

      **Citation Placement:**
      - Place citations immediately after claims, statistics, or facts: "Company revenue reached $100M [1]"
      - Use citations after quotes: "The CEO stated 'We're growing rapidly' [2]"
      - Cite financial data: "The company raised $50M in Series A funding [3]"
      - Reference market information: "The market size is estimated at $2B [4]"

      **Sources and References Section:**
      - ALWAYS include this section at the very end of your report
      - Title it exactly: "## Sources and References"
      - List sources in numerical order: 1., 2., 3., 4., 5., etc.
      - Format each entry as: "1. Source Title - URL - Date"
      - Example:
        1. Company Announces $100M Series A - TechCrunch - March 15, 2024
        2. CEO Interview on Growth Strategy - Forbes - March 10, 2024
        3. Market Analysis Report - McKinsey - February 2024

      **Citation Examples:**
      - "The company achieved $100M ARR in 2024 [1] and plans to expand internationally [2]."
      - "According to recent reports, the market opportunity is valued at $5B [3]."
      - "The CEO mentioned in an interview that hiring is a top priority [4]."

      **IMPORTANT:** Count your citations carefully and ensure they increment properly: [1], [2], [3], [4], [5], etc. Do NOT use [1] for multiple different sources.

      **CITATION EXAMPLE - CORRECT WAY:**
      "DevRev announced $100M Series A funding [1]. The company has grown to 554 employees [2]. According to Crunchbase, DevRev was founded in 2020 [3]. The CEO stated in a recent interview that AI is core to their strategy [4]."

      Sources and References:
      1. DevRev Announces $100M Series A - Yahoo Finance - August 9, 2024
      2. DevRev Company Profile - GetLatka - January 1, 2025
      3. DevRev Startup Profile - Crunchbase - October 17, 2024
      4. CEO Interview on AI Strategy - TechCrunch - March 2024

      **CITATION EXAMPLE - WRONG WAY (DO NOT DO THIS):**
      "DevRev announced $100M Series A funding [1]. The company has grown to 554 employees [1]. According to Crunchbase, DevRev was founded in 2020 [1]. The CEO stated in a recent interview that AI is core to their strategy [1]." ← This is WRONG because different sources all use [1]

      **BEFORE YOU START WRITING:**
      1. Use the webSearch tool to gather comprehensive information
      2. Create a mental list of all unique sources you will reference
      3. Assign each unique source a sequential number: Source A = [1], Source B = [2], Source C = [3], etc.
      4. Write your report using these assigned numbers consistently
      5. End with the "Sources and References" section listing all sources in numerical order

      **CITATION VERIFICATION CHECKLIST:**
      - ✓ Did I start with [1] for the first source?
      - ✓ Did I increment sequentially: [1], [2], [3], [4], [5]?
      - ✓ Did I avoid repeating [1] for different sources?
      - ✓ Did I use the same number when referencing the same source multiple times?
      - ✓ Did I include a "Sources and References" section at the end?
      - ✓ Are my reference numbers in the text consistent with the reference list?

      Always use the webSearch tool first to gather current, comprehensive information before providing your analysis. Be thorough, objective, and professional in your research approach.
    `,
    model,
    tools: { webSearch: webSearchTool }
  });
}
const companyResearcherAgent = createCompanyResearcherAgent();
const POST = async ({ request, locals, url }) => {
  const { session } = locals.auth;
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }
  const wantsStream = url.searchParams.get("stream") === "true";
  if (wantsStream) {
    return handleStreamingRequest(request);
  }
  try {
    const { message } = await request.json();
    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 });
    }
    console.log(
      "Researcher agent request received:",
      message.substring(0, 100) + "..."
    );
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () => reject(
          new Error("Agent timeout after 120 seconds - try a simpler query")
        ),
        12e4
      );
    });
    const agentPromise = companyResearcherAgent.generate([
      {
        role: "user",
        content: message
      }
    ]);
    const response = await Promise.race([agentPromise, timeoutPromise]);
    console.log(
      "Researcher agent response generated, length:",
      response.text?.length || 0
    );
    return json({
      response: response.text
    });
  } catch (error) {
    console.error("Error in researcher agent:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};
async function handleStreamingRequest(request) {
  const { message } = await request.json();
  if (!message || typeof message !== "string") {
    return json({ error: "Message is required" }, { status: 400 });
  }
  console.log(
    "Researcher streaming request received:",
    message.substring(0, 100) + "..."
  );
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      const send = (data) => {
        const chunk = encoder.encode(`data: ${JSON.stringify(data)}

`);
        controller.enqueue(chunk);
      };
      performResearchWithProgress(message, send).then(() => {
        controller.close();
      }).catch((error) => {
        console.error("Streaming error:", error);
        send({ error: "Research failed", details: error.message });
        controller.close();
      });
    }
  });
  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive"
    }
  });
}
async function performResearchWithProgress(message, send) {
  try {
    send({
      step: 1,
      action: "Analyzing research request and extracting company information...",
      progress: 5,
      status: "active"
    });
    await new Promise((resolve) => setTimeout(resolve, 1500));
    send({
      step: 1,
      action: "Research scope identified",
      progress: 15,
      status: "completed"
    });
    send({
      step: 2,
      action: "Conducting comprehensive web search for company information...",
      progress: 20,
      status: "active"
    });
    await new Promise((resolve) => setTimeout(resolve, 3e3));
    send({
      step: 2,
      action: "Found relevant sources and company data",
      progress: 40,
      status: "completed"
    });
    send({
      step: 3,
      action: "Gathering financial performance and metrics data...",
      progress: 45,
      status: "active"
    });
    await new Promise((resolve) => setTimeout(resolve, 2500));
    send({
      step: 3,
      action: "Financial analysis completed",
      progress: 60,
      status: "completed"
    });
    send({
      step: 4,
      action: "Analyzing market position and competitive landscape...",
      progress: 65,
      status: "active"
    });
    await new Promise((resolve) => setTimeout(resolve, 2e3));
    send({
      step: 4,
      action: "Market analysis and competitive research completed",
      progress: 80,
      status: "completed"
    });
    send({
      step: 5,
      action: "Generating comprehensive research report...",
      progress: 85,
      status: "active"
    });
    const response = await companyResearcherAgent.generate([
      {
        role: "user",
        content: message
      }
    ]);
    send({
      step: 5,
      action: "Research report generated successfully!",
      progress: 100,
      status: "completed"
    });
    send({
      type: "final_response",
      response: response.text
    });
  } catch (error) {
    console.error("Research process failed:", error);
    throw error;
  }
}
export {
  POST
};

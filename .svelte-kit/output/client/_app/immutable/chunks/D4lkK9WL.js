import{o as a,c as e}from"./CbBNkXRp.js";const t=a({email:e().email(),code:e().length(6,"Must be a 6-digit code").regex(/^\d+$/,"Must be a 6-digit code")}),r=a({email:e().email("Invalid email address"),password:e().min(8,"Password must be at least 8 characters long").max(16,"Password must be at most 16 characters long").regex(/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,16}$/,"For security sake, please include lowercase, uppercase letters and digits."),confirmPassword:e()}).refine(({password:s,confirmPassword:o})=>s===o,{message:"The passwords did not match"}),m=a({name:e().min(1,"The environment name is required").max(30,"The environment name can be at most 72 charaters long").regex(/^[a-z0-9+$]/i,"Environment name must be alphanumeric")}),i=a({email:e().email()}),c=a({currentPassword:e().min(1,"You must include your current password. If you forgot it, sign out then use 'forgot password' on the sign in page.").optional(),newPassword1:e().min(6,"The new password must be at least 6 charaters long").max(72,"The new password can be at most 72 charaters long"),newPassword2:e().min(6,"The new password must be at least 6 charaters long").max(72,"The new password can be at most 72 charaters long")}).refine(({newPassword1:s,newPassword2:o})=>s===o,"The passwords don't match"),d=a({currentPassword:e().min(1,"You must provide your current password to delete your account. If you forgot it, sign out then use 'forgot password' on the sign in page.")}),l=a({first_name:e().min(2,"First name is required").max(500,"First name too long"),last_name:e().min(2,"Last name is required").max(500,"Last name too long"),email:e().email("Email is required").max(500,"Email too long"),company_name:e().max(500,"Company too long"),phone:e().max(100,"Phone number").optional(),message_body:e().max(2e3,"Message too long. Must be no more than 2000 character").default("")}),u=a({full_name:e().min(1,"Name is required").max(50,"Name must be less than 50 characters"),company_name:e().min(1,"Company name is required. If this is a hobby project or personal app, please put your name.").max(50,"Company name must be less than 50 characters"),website:e().min(1,"Company website is required. An app store URL is a good alternative if you don't have a website.").max(50,"Name must be less than 50 characters")});export{m as a,l as b,c,d,i as e,t as o,u as p,r as s};

import"./CWj6FrbW.js";import"./Cvx8ZW61.js";import{b as d,e as l,a as p}from"./wnqW1tdD.js";import{s as c}from"./BDqVm3Gq.js";import{l as i,s as $}from"./Cmdkv-7M.js";import{I as h}from"./CX_t0Ed_.js";function A(e,a){const o=i(a,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18"}]];h(e,$({name:"brain"},()=>o,{get iconNode(){return n},children:(s,m)=>{var t=d(),r=l(t);c(r,a,"default",{},null),p(s,t)},$$slots:{default:!0}}))}function N(e,a){const o=i(a,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16"}],["path",{d:"M18 17V9"}],["path",{d:"M13 17V5"}],["path",{d:"M8 17v-3"}]];h(e,$({name:"chart-column"},()=>o,{get iconNode(){return n},children:(s,m)=>{var t=d(),r=l(t);c(r,a,"default",{},null),p(s,t)},$$slots:{default:!0}}))}function z(e,a){const o=i(a,["children","$$slots","$$events","$$legacy"]),n=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"}]];h(e,$({name:"zap"},()=>o,{get iconNode(){return n},children:(s,m)=>{var t=d(),r=l(t);c(r,a,"default",{},null),p(s,t)},$$slots:{default:!0}}))}export{A as B,N as C,z as Z};

import{c as T}from"./BODTVNWu.js";const re=globalThis.__sveltekit_bdxwet.env,I="0.5.2";/*!
 * cookie
 * Copyright(c) 2012-2014 <PERSON>
 * Copyright(c) 2015 <PERSON>
 * MIT Licensed
 */var U=P,R=j,_=Object.prototype.toString,q=Object.prototype.hasOwnProperty,B=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,$=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,L=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,N=/^[\u0020-\u003A\u003D-\u007E]*$/;function P(e,t){if(typeof e!="string")throw new TypeError("argument str must be a string");var r={},i=e.length;if(i<2)return r;var u=t&&t.decode||z,n=0,o=0,s=0;do{if(o=e.indexOf("=",n),o===-1)break;if(s=e.indexOf(";",n),s===-1)s=i;else if(o>s){n=e.lastIndexOf(";",o-1)+1;continue}var a=S(e,n,o),c=y(e,o,a),h=e.slice(a,c);if(!q.call(r,h)){var l=S(e,o+1,s),f=y(e,s,l);e.charCodeAt(l)===34&&e.charCodeAt(f-1)===34&&(l++,f--);var g=e.slice(l,f);r[h]=D(g,u)}n=s+1}while(n<i);return r}function S(e,t,r){do{var i=e.charCodeAt(t);if(i!==32&&i!==9)return t}while(++t<r);return r}function y(e,t,r){for(;t>r;){var i=e.charCodeAt(--t);if(i!==32&&i!==9)return t+1}return r}function j(e,t,r){var i=r&&r.encode||encodeURIComponent;if(typeof i!="function")throw new TypeError("option encode is invalid");if(!B.test(e))throw new TypeError("argument name is invalid");var u=i(t);if(!$.test(u))throw new TypeError("argument val is invalid");var n=e+"="+u;if(!r)return n;if(r.maxAge!=null){var o=Math.floor(r.maxAge);if(!isFinite(o))throw new TypeError("option maxAge is invalid");n+="; Max-Age="+o}if(r.domain){if(!L.test(r.domain))throw new TypeError("option domain is invalid");n+="; Domain="+r.domain}if(r.path){if(!N.test(r.path))throw new TypeError("option path is invalid");n+="; Path="+r.path}if(r.expires){var s=r.expires;if(!F(s)||isNaN(s.valueOf()))throw new TypeError("option expires is invalid");n+="; Expires="+s.toUTCString()}if(r.httpOnly&&(n+="; HttpOnly"),r.secure&&(n+="; Secure"),r.partitioned&&(n+="; Partitioned"),r.priority){var a=typeof r.priority=="string"?r.priority.toLowerCase():r.priority;switch(a){case"low":n+="; Priority=Low";break;case"medium":n+="; Priority=Medium";break;case"high":n+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}}if(r.sameSite){var c=typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite;switch(c){case!0:n+="; SameSite=Strict";break;case"lax":n+="; SameSite=Lax";break;case"strict":n+="; SameSite=Strict";break;case"none":n+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return n}function z(e){return e.indexOf("%")!==-1?decodeURIComponent(e):e}function F(e){return _.call(e)==="[object Date]"}function D(e,t){try{return t(e)}catch{return e}}function w(){return typeof window<"u"&&typeof window.document<"u"}const m={path:"/",sameSite:"lax",httpOnly:!1,maxAge:400*24*60*60},H=3180,M=/^(.*)[.](0|[1-9][0-9]*)$/;function b(e,t){if(e===t)return!0;const r=e.match(M);return!!(r&&r[1]===t)}function V(e,t,r){const i=H;let u=encodeURIComponent(t);if(u.length<=i)return[{name:e,value:t}];const n=[];for(;u.length>0;){let o=u.slice(0,i);const s=o.lastIndexOf("%");s>i-3&&(o=o.slice(0,s));let a="";for(;o.length>0;)try{a=decodeURIComponent(o);break}catch(c){if(c instanceof URIError&&o.at(-3)==="%"&&o.length>3)o=o.slice(0,o.length-3);else throw c}n.push(a),u=u.slice(o.length)}return n.map((o,s)=>({name:`${e}.${s}`,value:o}))}async function X(e,t){const r=await t(e);if(r)return r;let i=[];for(let u=0;;u++){const n=`${e}.${u}`,o=await t(n);if(!o)break;i.push(o)}return i.length>0?i.join(""):null}const v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),C=` 	
\r=`.split(""),G=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<C.length;t+=1)e[C[t].charCodeAt(0)]=-2;for(let t=0;t<v.length;t+=1)e[v[t].charCodeAt(0)]=t;return e})();function K(e){const t=[];let r=0,i=0;if(Y(e,n=>{for(r=r<<8|n,i+=8;i>=6;){const o=r>>i-6&63;t.push(v[o]),i-=6}}),i>0)for(r=r<<6-i,i=6;i>=6;){const n=r>>i-6&63;t.push(v[n]),i-=6}return t.join("")}function W(e){const t=[],r=o=>{t.push(String.fromCodePoint(o))},i={utf8seq:0,codepoint:0};let u=0,n=0;for(let o=0;o<e.length;o+=1){const s=e.charCodeAt(o),a=G[s];if(a>-1)for(u=u<<6|a,n+=6;n>=8;)J(u>>n-8&255,i,r),n-=8;else{if(a===-2)continue;throw new Error(`Invalid Base64-URL character "${e.at(o)}" at position ${o}`)}}return t.join("")}function Z(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function Y(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){const u=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|u)+65536,r+=1}Z(i,t)}}function J(e,t,r){if(t.utf8seq===0){if(e<=127){r(e);return}for(let i=1;i<6;i+=1)if(!(e>>7-i&1)){t.utf8seq=i;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&r(t.codepoint)}}const p="base64-";function Q(e,t){const r=e.cookies??null,i={},u={};let n,o;if(r)if("get"in r){const s=async a=>{const c=a.flatMap(l=>[l,...Array.from({length:5}).map((f,g)=>`${l}.${g}`)]),h=[];for(let l=0;l<c.length;l+=1){const f=await r.get(c[l]);!f&&typeof f!="string"||h.push({name:c[l],value:f})}return h};if(n=async a=>await s(a),"set"in r&&"remove"in r)o=async a=>{for(let c=0;c<a.length;c+=1){const{name:h,value:l,options:f}=a[c];l?await r.set(h,l,f):await r.remove(h,f)}};else throw new Error("@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)")}else if("getAll"in r)if(n=async()=>await r.getAll(),"setAll"in r)o=r.setAll;else throw new Error("@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)");else throw new Error(`@supabase/ssr: createBrowserClient requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${w()?" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.":""}`);else if(w()){const s=()=>{const a=U(document.cookie);return Object.keys(a).map(c=>({name:c,value:a[c]}))};n=()=>s(),o=a=>{a.forEach(({name:c,value:h,options:l})=>{document.cookie=R(c,h,l)})}}else n=()=>[],o=()=>{throw new Error("@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed")};return{getAll:n,setAll:o,setItems:i,removedItems:u,storage:{isServer:!1,getItem:async s=>{const a=await n([s]),c=await X(s,async l=>{const f=(a==null?void 0:a.find(({name:g})=>g===l))||null;return f?f.value:null});if(!c)return null;let h=c;return c.startsWith(p)&&(h=W(c.substring(p.length))),h},setItem:async(s,a)=>{const c=await n([s]),h=(c==null?void 0:c.map(({name:d})=>d))||[],l=new Set(h.filter(d=>b(d,s)));let f=a;f=p+K(a);const g=V(s,f);g.forEach(({name:d})=>{l.delete(d)});const A={...m,...e==null?void 0:e.cookieOptions,maxAge:0},k={...m,...e==null?void 0:e.cookieOptions,maxAge:m.maxAge};delete A.name,delete k.name;const E=[...[...l].map(d=>({name:d,value:"",options:A})),...g.map(({name:d,value:O})=>({name:d,value:O,options:k}))];E.length>0&&await o(E)},removeItem:async s=>{const a=await n([s]),h=((a==null?void 0:a.map(({name:f})=>f))||[]).filter(f=>b(f,s)),l={...m,...e==null?void 0:e.cookieOptions,maxAge:0};delete l.name,h.length>0&&await o(h.map(f=>({name:f,value:"",options:l})))}}}}let x;function te(e,t,r){var o;const i=w();if(i&&x)return x;if(!e||!t)throw new Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);const{storage:u}=Q({...r}),n=T(e,t,{...r,global:{...r==null?void 0:r.global,headers:{...(o=r==null?void 0:r.global)==null?void 0:o.headers,"X-Client-Info":`supabase-ssr/${I}`}},auth:{...r==null?void 0:r.auth,flowType:"pkce",autoRefreshToken:w(),detectSessionInUrl:w(),persistSession:!0,storage:u}});return i&&(x=n),n}export{te as c,re as e};

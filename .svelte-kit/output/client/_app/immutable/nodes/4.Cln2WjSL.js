var kt=Object.defineProperty;var Ot=(u,e,x)=>e in u?kt(u,e,{enumerable:!0,configurable:!0,writable:!0,value:x}):u[e]=x;var Ke=(u,e,x)=>Ot(u,typeof e!="symbol"?e+"":e,x);import"../chunks/CWj6FrbW.js";import{b as se,e as X,a as s,b6 as Tt,ar as Et,as as Pt,p as Ce,l as $,aM as ce,h as ze,g as we,k as ge,m as Ve,j as t,f as H,c as F,r as A,Y as l,d as gt,i as bt,s as oe,n as St,u as Le,J as Ue,a7 as Dt,t as Oe,v as Ze}from"../chunks/wnqW1tdD.js";import{e as d,d as At,s as We}from"../chunks/CDPCzm7q.js";import{s as Ft}from"../chunks/yk44OJLy.js";import{i as q}from"../chunks/BjRbZGyQ.js";import{e as st,i as lt}from"../chunks/CsnEE4l9.js";import{c as $e}from"../chunks/ojdN50pv.js";import{a as ue,c as Nt,s as pe}from"../chunks/rh_XW2Tv.js";import{s as Ne}from"../chunks/Bz0_kaay.js";import{s as xe,a as Ae}from"../chunks/D5ITLM2v.js";import{f as jt,a as zt}from"../chunks/CiB29Aqe.js";import"../chunks/DpzY6icx.js";import"../chunks/Cvx8ZW61.js";import{o as It,w as Mt,m as Pe,e as et,a as Ye,k as qe,f as Rt,u as dt,n as Se,s as ct,j as je,p as Vt,i as ut,l as Ht}from"../chunks/D477uqMC.js";import{s as ee}from"../chunks/BDqVm3Gq.js";import{i as Te}from"../chunks/BxG_UISn.js";import{l as te,s as Ie,p}from"../chunks/Cmdkv-7M.js";import{w as Bt,d as ht}from"../chunks/BvpDAKCq.js";import{t as ft,g as Kt,o as Lt,r as Ut,b as Wt}from"../chunks/CuJHdp1M.js";import{u as Yt,c as qt,g as Gt,a as Jt,h as vt,r as Xt,S as Qt,M as Zt}from"../chunks/CLUknB3d.js";import{c as $t,a as mt}from"../chunks/DPaidA8O.js";import{a as fe}from"../chunks/Cet13GU7.js";import{b as ve}from"../chunks/D5U2DSnR.js";import{p as ea}from"../chunks/idBKwYq8.js";import{g as ta}from"../chunks/DyGaIYLH.js";import{t as ye}from"../chunks/sBNKiCwy.js";import{b}from"../chunks/B2uh23P-.js";import{X as aa}from"../chunks/BAo0SQ6-.js";import{c as Ge,f as na}from"../chunks/BMdVdstb.js";import{C as ra}from"../chunks/ZAWXEYb0.js";import{I as tt}from"../chunks/CX_t0Ed_.js";function oa(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=[["path",{d:"m15 18-6-6 6-6"}]];tt(u,Ie({name:"chevron-left"},()=>x,{get iconNode(){return y},children:(_,z)=>{var f=se(),a=X(f);ee(a,e,"default",{},null),s(_,f)},$$slots:{default:!0}}))}function ia(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}],["polyline",{points:"16 17 21 12 16 7"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12"}]];tt(u,Ie({name:"log-out"},()=>x,{get iconNode(){return y},children:(_,z)=>{var f=se(),a=X(f);ee(a,e,"default",{},null),s(_,f)},$$slots:{default:!0}}))}function sa(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"}],["circle",{cx:"12",cy:"12",r:"3"}]];tt(u,Ie({name:"settings"},()=>x,{get iconNode(){return y},children:(_,z)=>{var f=se(),a=X(f);ee(a,e,"default",{},null),s(_,f)},$$slots:{default:!0}}))}const{name:De}=Rt("dialog"),la={preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,role:"dialog",defaultOpen:!1,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},da=["content","title","description"];function ca(u){const e={...la,...u},x=ft(It(e,"ids")),{preventScroll:y,closeOnEscape:_,closeOnOutsideClick:z,role:f,portal:a,forceVisible:o,openFocus:P,closeFocus:Q,onOutsideClick:I}=x,T=Mt.writable(null),C=ft({...Kt(da),...e.ids}),U=e.open??Bt(e.defaultOpen),w=Lt(U,e==null?void 0:e.onOpenChange),k=ht([w,o],([r,N])=>r||N);let O=Se;function h(r){const N=r.currentTarget,v=r.currentTarget;!ut(N)||!ut(v)||(w.set(!0),T.set(v))}function c(){w.set(!1),vt({prop:Q.get(),defaultEl:T.get()})}const S=Pe(De("trigger"),{stores:[w],returned:([r])=>({"aria-haspopup":"dialog","aria-expanded":r,type:"button"}),action:r=>({destroy:et(Ye(r,"click",v=>{h(v)}),Ye(r,"keydown",v=>{v.key!==qe.ENTER&&v.key!==qe.SPACE||(v.preventDefault(),h(v))}))})}),B=Pe(De("overlay"),{stores:[k,w],returned:([r,N])=>({hidden:r?void 0:!0,tabindex:-1,style:ct({display:r?void 0:"none"}),"aria-hidden":!0,"data-state":N?"open":"closed"}),action:r=>{let N=Se;if(_.get()){const v=dt(r,{handler:()=>{c()}});v&&v.destroy&&(N=v.destroy)}return{destroy(){N()}}}}),ae=Pe(De("content"),{stores:[k,C.content,C.description,C.title,w],returned:([r,N,v,G,M])=>({id:N,role:f.get(),"aria-describedby":v,"aria-labelledby":G,"aria-modal":r?"true":void 0,"data-state":M?"open":"closed",tabindex:-1,hidden:r?void 0:!0,style:ct({display:r?void 0:"none"})}),action:r=>{let N=Se,v=Se;const G=et(je([w,z,_],([M,R,W])=>{if(!M)return;const m=qt({immediate:!1,escapeDeactivates:W,clickOutsideDeactivates:R,allowOutsideClick:!0,returnFocusOnDeactivate:!1,fallbackFocus:r});N=m.activate,v=m.deactivate;const Y=m.useFocusTrap(r);return Y&&Y.destroy?Y.destroy:m.deactivate}),je([z,w],([M,R])=>Yt(r,{open:R,closeOnInteractOutside:M,onClose(){c()},shouldCloseOnInteractOutside(W){var m;return(m=I.get())==null||m(W),!W.defaultPrevented}}).destroy),je([_],([M])=>M?dt(r,{handler:c}).destroy:Se),je([k],([M])=>{Tt().then(()=>{M?N():v()})}));return{destroy:()=>{O(),G()}}}}),ne=Pe(De("portalled"),{stores:a,returned:r=>({"data-portal":Vt(r)}),action:r=>{const N=je([a],([v])=>{if(v===null)return Se;const G=Gt(r,v);return G===null?Se:Jt(r,G).destroy});return{destroy(){N()}}}}),Ee=Pe(De("title"),{stores:[C.title],returned:([r])=>({id:r})}),be=Pe(De("description"),{stores:[C.description],returned:([r])=>({id:r})}),me=Pe(De("close"),{returned:()=>({type:"button"}),action:r=>({destroy:et(Ye(r,"click",()=>{c()}),Ye(r,"keydown",v=>{v.key!==qe.SPACE&&v.key!==qe.ENTER||(v.preventDefault(),c())}))})});return je([w,y],([r,N])=>{if(Ht){if(N&&r&&(O=Xt()),r){const v=document.getElementById(C.content.get());vt({prop:P.get(),defaultEl:v})}return()=>{o.get()||O()}}}),{ids:C,elements:{content:ae,trigger:S,title:Ee,description:be,overlay:B,close:me,portalled:ne},states:{open:w},options:x}}function _t(){return{NAME:"dialog",PARTS:["close","content","description","overlay","portal","title","trigger"]}}function ua(u){const{NAME:e,PARTS:x}=_t(),y=$t(e,x),_={...ca({...Ut(u),role:"dialog",forceVisible:!0}),getAttrs:y};return Pt(e,_),{..._,updateOption:Wt(_.options)}}function He(){const{NAME:u}=_t();return Et(u)}function fa(u,e){Ce(e,!1);const[x,y]=Ae(),_=()=>xe(O,"$idValues",x);let z=p(e,"preventScroll",24,()=>{}),f=p(e,"closeOnEscape",24,()=>{}),a=p(e,"closeOnOutsideClick",24,()=>{}),o=p(e,"portal",24,()=>{}),P=p(e,"open",28,()=>{}),Q=p(e,"onOpenChange",24,()=>{}),I=p(e,"openFocus",24,()=>{}),T=p(e,"closeFocus",24,()=>{}),C=p(e,"onOutsideClick",24,()=>{});const{states:{open:U},updateOption:w,ids:k}=ua({closeOnEscape:f(),preventScroll:z(),closeOnOutsideClick:a(),portal:o(),forceVisible:!0,defaultOpen:P(),openFocus:I(),closeFocus:T(),onOutsideClick:C(),onOpenChange:({next:S})=>{var B;return P()!==S&&((B=Q())==null||B(S),P(S)),S}}),O=ht([k.content,k.description,k.title],([S,B,ae])=>({content:S,description:B,title:ae}));$(()=>ce(P()),()=>{P()!==void 0&&U.set(P())}),$(()=>ce(z()),()=>{w("preventScroll",z())}),$(()=>ce(f()),()=>{w("closeOnEscape",f())}),$(()=>ce(a()),()=>{w("closeOnOutsideClick",a())}),$(()=>ce(o()),()=>{w("portal",o())}),$(()=>ce(I()),()=>{w("openFocus",I())}),$(()=>ce(T()),()=>{w("closeFocus",T())}),$(()=>ce(C()),()=>{w("onOutsideClick",C())}),ze(),Te();var h=se(),c=X(h);ee(c,e,"default",{get ids(){return _()}},null),s(u,h),we(),y()}var va=H("<button><!></button>");function ga(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=te(x,["asChild","el"]);Ce(e,!1);const[_,z]=Ae(),f=()=>xe(Q,"$close",_),a=Ve();let o=p(e,"asChild",8,!1),P=p(e,"el",28,()=>{});const{elements:{close:Q},getAttrs:I}=He(),T=mt(),C=I("close");$(()=>f(),()=>{ge(a,f())}),$(()=>t(a),()=>{Object.assign(t(a),C)}),ze(),Te();var U=se(),w=X(U);{var k=h=>{var c=se(),S=X(c);ee(S,e,"default",{get builder(){return t(a)}},null),s(h,c)},O=h=>{var c=va();ue(c,()=>({...t(a),type:"button",...y}));var S=F(c);ee(S,e,"default",{get builder(){return t(a)}},null),A(c),ve(c,B=>P(B),()=>P()),fe(c,B=>{var ae,ne;return(ne=(ae=t(a)).action)==null?void 0:ne.call(ae,B)}),l(()=>d("m-click",c,T)),l(()=>d("m-keydown",c,T)),s(h,c)};q(w,h=>{o()?h(k):h(O,!1)})}s(u,U),we(),z()}var ba=H("<div><!></div>");function ha(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=te(x,["asChild","el"]);Ce(e,!1);const[_,z]=Ae(),f=()=>xe(Q,"$portalled",_),a=Ve();let o=p(e,"asChild",8,!1),P=p(e,"el",28,()=>{});const{elements:{portalled:Q},getAttrs:I}=He(),T=I("portal");$(()=>f(),()=>{ge(a,f())}),$(()=>t(a),()=>{Object.assign(t(a),T)}),ze(),Te();var C=se(),U=X(C);{var w=O=>{var h=se(),c=X(h);ee(c,e,"default",{get builder(){return t(a)}},null),s(O,h)},k=O=>{var h=ba();ue(h,()=>({...t(a),...y}));var c=F(h);ee(c,e,"default",{get builder(){return t(a)}},null),A(h),ve(h,S=>P(S),()=>P()),fe(h,S=>{var B,ae;return(ae=(B=t(a)).action)==null?void 0:ae.call(B,S)}),s(O,h)};q(U,O=>{o()?O(w):O(k,!1)})}s(u,C),we(),z()}var ma=H("<div><!></div>"),_a=H("<div><!></div>"),pa=H("<div><!></div>"),ya=H("<div><!></div>"),xa=H("<div><!></div>");function Ca(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=te(x,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","el"]);Ce(e,!1);const[_,z]=Ae(),f=()=>xe(h,"$content",_),a=()=>xe(c,"$open",_),o=Ve();let P=p(e,"transition",24,()=>{}),Q=p(e,"transitionConfig",24,()=>{}),I=p(e,"inTransition",24,()=>{}),T=p(e,"inTransitionConfig",24,()=>{}),C=p(e,"outTransition",24,()=>{}),U=p(e,"outTransitionConfig",24,()=>{}),w=p(e,"asChild",8,!1),k=p(e,"id",24,()=>{}),O=p(e,"el",28,()=>{});const{elements:{content:h},states:{open:c},ids:S,getAttrs:B}=He(),ae=B("content");$(()=>ce(k()),()=>{k()&&S.content.set(k())}),$(()=>f(),()=>{ge(o,f())}),$(()=>t(o),()=>{Object.assign(t(o),ae)}),ze(),Te();var ne=se(),Ee=X(ne);{var be=r=>{var N=se(),v=X(N);ee(v,e,"default",{get builder(){return t(o)}},null),s(r,N)},me=(r,N)=>{{var v=M=>{var R=ma();ue(R,()=>({...t(o),...y}));var W=F(R);ee(W,e,"default",{get builder(){return t(o)}},null),A(R),ve(R,m=>O(m),()=>O()),fe(R,m=>{var Y,V;return(V=(Y=t(o)).action)==null?void 0:V.call(Y,m)}),l(()=>d("pointerdown",R,function(m){b.call(this,e,m)})),l(()=>d("pointermove",R,function(m){b.call(this,e,m)})),l(()=>d("pointerup",R,function(m){b.call(this,e,m)})),l(()=>d("touchcancel",R,function(m){b.call(this,e,m)})),l(()=>d("touchend",R,function(m){b.call(this,e,m)})),l(()=>d("touchmove",R,function(m){b.call(this,e,m)},void 0,!1)),l(()=>d("touchstart",R,function(m){b.call(this,e,m)},void 0,!1)),ye(3,R,P,Q),s(M,R)},G=(M,R)=>{{var W=Y=>{var V=_a();ue(V,()=>({...t(o),...y}));var re=F(V);ee(re,e,"default",{get builder(){return t(o)}},null),A(V),ve(V,E=>O(E),()=>O()),fe(V,E=>{var J,K;return(K=(J=t(o)).action)==null?void 0:K.call(J,E)}),l(()=>d("pointerdown",V,function(E){b.call(this,e,E)})),l(()=>d("pointermove",V,function(E){b.call(this,e,E)})),l(()=>d("pointerup",V,function(E){b.call(this,e,E)})),l(()=>d("touchcancel",V,function(E){b.call(this,e,E)})),l(()=>d("touchend",V,function(E){b.call(this,e,E)})),l(()=>d("touchmove",V,function(E){b.call(this,e,E)},void 0,!1)),l(()=>d("touchstart",V,function(E){b.call(this,e,E)},void 0,!1)),ye(1,V,I,T),ye(2,V,C,U),s(Y,V)},m=(Y,V)=>{{var re=J=>{var K=pa();ue(K,()=>({...t(o),...y}));var ie=F(K);ee(ie,e,"default",{get builder(){return t(o)}},null),A(K),ve(K,D=>O(D),()=>O()),fe(K,D=>{var Z,n;return(n=(Z=t(o)).action)==null?void 0:n.call(Z,D)}),l(()=>d("pointerdown",K,function(D){b.call(this,e,D)})),l(()=>d("pointermove",K,function(D){b.call(this,e,D)})),l(()=>d("pointerup",K,function(D){b.call(this,e,D)})),l(()=>d("touchcancel",K,function(D){b.call(this,e,D)})),l(()=>d("touchend",K,function(D){b.call(this,e,D)})),l(()=>d("touchmove",K,function(D){b.call(this,e,D)},void 0,!1)),l(()=>d("touchstart",K,function(D){b.call(this,e,D)},void 0,!1)),ye(1,K,I,T),s(J,K)},E=(J,K)=>{{var ie=Z=>{var n=ya();ue(n,()=>({...t(o),...y}));var g=F(n);ee(g,e,"default",{get builder(){return t(o)}},null),A(n),ve(n,i=>O(i),()=>O()),fe(n,i=>{var L,le;return(le=(L=t(o)).action)==null?void 0:le.call(L,i)}),l(()=>d("pointerdown",n,function(i){b.call(this,e,i)})),l(()=>d("pointermove",n,function(i){b.call(this,e,i)})),l(()=>d("pointerup",n,function(i){b.call(this,e,i)})),l(()=>d("touchcancel",n,function(i){b.call(this,e,i)})),l(()=>d("touchend",n,function(i){b.call(this,e,i)})),l(()=>d("touchmove",n,function(i){b.call(this,e,i)},void 0,!1)),l(()=>d("touchstart",n,function(i){b.call(this,e,i)},void 0,!1)),ye(2,n,C,U),s(Z,n)},D=(Z,n)=>{{var g=i=>{var L=xa();ue(L,()=>({...t(o),...y}));var le=F(L);ee(le,e,"default",{get builder(){return t(o)}},null),A(L),ve(L,j=>O(j),()=>O()),fe(L,j=>{var de,he;return(he=(de=t(o)).action)==null?void 0:he.call(de,j)}),l(()=>d("pointerdown",L,function(j){b.call(this,e,j)})),l(()=>d("pointermove",L,function(j){b.call(this,e,j)})),l(()=>d("pointerup",L,function(j){b.call(this,e,j)})),l(()=>d("touchcancel",L,function(j){b.call(this,e,j)})),l(()=>d("touchend",L,function(j){b.call(this,e,j)})),l(()=>d("touchmove",L,function(j){b.call(this,e,j)},void 0,!1)),l(()=>d("touchstart",L,function(j){b.call(this,e,j)},void 0,!1)),s(i,L)};q(Z,i=>{a()&&i(g)},n)}};q(J,Z=>{C()&&a()?Z(ie):Z(D,!1)},K)}};q(Y,J=>{I()&&a()?J(re):J(E,!1)},V)}};q(M,Y=>{I()&&C()&&a()?Y(W):Y(m,!1)},R)}};q(r,M=>{P()&&a()?M(v):M(G,!1)},N)}};q(Ee,r=>{w()&&a()?r(be):r(me,!1)})}s(u,ne),we(),z()}var wa=H("<div></div>"),ka=H("<div></div>"),Oa=H("<div></div>"),Ta=H("<div></div>"),Ea=H("<div></div>");function Pa(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=te(x,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","el"]);Ce(e,!1);const[_,z]=Ae(),f=()=>xe(O,"$overlay",_),a=()=>xe(h,"$open",_),o=Ve();let P=p(e,"transition",24,()=>{}),Q=p(e,"transitionConfig",24,()=>{}),I=p(e,"inTransition",24,()=>{}),T=p(e,"inTransitionConfig",24,()=>{}),C=p(e,"outTransition",24,()=>{}),U=p(e,"outTransitionConfig",24,()=>{}),w=p(e,"asChild",8,!1),k=p(e,"el",28,()=>{});const{elements:{overlay:O},states:{open:h},getAttrs:c}=He(),S=c("overlay");$(()=>f(),()=>{ge(o,f())}),$(()=>t(o),()=>{Object.assign(t(o),S)}),ze(),Te();var B=se(),ae=X(B);{var ne=be=>{var me=se(),r=X(me);ee(r,e,"default",{get builder(){return t(o)}},null),s(be,me)},Ee=(be,me)=>{{var r=v=>{var G=wa();ue(G,()=>({...t(o),...y})),l(()=>d("mouseup",G,function(M){b.call(this,e,M)})),ve(G,M=>k(M),()=>k()),fe(G,M=>{var R,W;return(W=(R=t(o)).action)==null?void 0:W.call(R,M)}),ye(3,G,P,Q),s(v,G)},N=(v,G)=>{{var M=W=>{var m=ka();ue(m,()=>({...t(o),...y})),ve(m,Y=>k(Y),()=>k()),fe(m,Y=>{var V,re;return(re=(V=t(o)).action)==null?void 0:re.call(V,Y)}),l(()=>d("mouseup",m,function(Y){b.call(this,e,Y)})),ye(1,m,I,T),ye(2,m,C,U),s(W,m)},R=(W,m)=>{{var Y=re=>{var E=Oa();ue(E,()=>({...t(o),...y})),ve(E,J=>k(J),()=>k()),fe(E,J=>{var K,ie;return(ie=(K=t(o)).action)==null?void 0:ie.call(K,J)}),l(()=>d("mouseup",E,function(J){b.call(this,e,J)})),ye(1,E,I,T),s(re,E)},V=(re,E)=>{{var J=ie=>{var D=Ta();ue(D,()=>({...t(o),...y})),ve(D,Z=>k(Z),()=>k()),fe(D,Z=>{var n,g;return(g=(n=t(o)).action)==null?void 0:g.call(n,Z)}),l(()=>d("mouseup",D,function(Z){b.call(this,e,Z)})),ye(2,D,C,U),s(ie,D)},K=(ie,D)=>{{var Z=n=>{var g=Ea();ue(g,()=>({...t(o),...y})),ve(g,i=>k(i),()=>k()),fe(g,i=>{var L,le;return(le=(L=t(o)).action)==null?void 0:le.call(L,i)}),l(()=>d("mouseup",g,function(i){b.call(this,e,i)})),s(n,g)};q(ie,n=>{a()&&n(Z)},D)}};q(re,ie=>{C()&&a()?ie(J):ie(K,!1)},E)}};q(W,re=>{I()&&a()?re(Y):re(V,!1)},m)}};q(v,W=>{I()&&C()&&a()?W(M):W(R,!1)},G)}};q(be,v=>{P()&&a()?v(r):v(N,!1)},me)}};q(ae,be=>{w()&&a()?be(ne):be(Ee,!1)})}s(u,B),we(),z()}var Sa=H("<button><!></button>");function Da(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=te(x,["asChild","el"]);Ce(e,!1);const[_,z]=Ae(),f=()=>xe(Q,"$trigger",_),a=Ve();let o=p(e,"asChild",8,!1),P=p(e,"el",28,()=>{});const{elements:{trigger:Q},getAttrs:I}=He(),T=mt(),C=I("trigger");$(()=>f(),()=>{ge(a,f())}),$(()=>t(a),()=>{Object.assign(t(a),C)}),ze(),Te();var U=se(),w=X(U);{var k=h=>{var c=se(),S=X(c);ee(S,e,"default",{get builder(){return t(a)}},null),s(h,c)},O=h=>{var c=Sa();ue(c,()=>({...t(a),type:"button",...y}));var S=F(c);ee(S,e,"default",{get builder(){return t(a)}},null),A(c),ve(c,B=>P(B),()=>P()),fe(c,B=>{var ae,ne;return(ne=(ae=t(a)).action)==null?void 0:ne.call(ae,B)}),l(()=>d("m-click",c,T)),l(()=>d("m-keydown",c,T)),s(h,c)};q(w,h=>{o()?h(k):h(O,!1)})}s(u,U),we(),z()}function Aa(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=te(x,[]);ha(u,Ie(()=>y,{children:(_,z)=>{var f=se(),a=X(f);ee(a,e,"default",{},null),s(_,f)},$$slots:{default:!0}}))}function Fa(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=te(x,["class","transition","transitionConfig"]);Ce(e,!1);let _=p(e,"class",8,void 0),z=p(e,"transition",8,jt),f=p(e,"transitionConfig",24,()=>({duration:150}));Te();const a=gt(()=>(ce(Ge),ce(_()),bt(()=>Ge("bg-background/80 fixed inset-0 z-50 backdrop-blur-sm",_()))));Pa(u,Ie({get transition(){return z()},get transitionConfig(){return f()},get class(){return t(a)}},()=>y)),we()}var Na=H('<!> <span class="sr-only">Close</span>',1),ja=H("<!> <!>",1),za=H("<!> <!>",1);function Ia(u,e){const x=te(e,["children","$$slots","$$events","$$legacy"]),y=te(x,["class","transition","transitionConfig"]);Ce(e,!1);let _=p(e,"class",8,void 0),z=p(e,"transition",8,na),f=p(e,"transitionConfig",24,()=>({duration:200}));Te(),Aa(u,{children:(a,o)=>{var P=za(),Q=X(P);Fa(Q,{});var I=oe(Q,2);const T=gt(()=>(ce(Ge),ce(_()),bt(()=>Ge("bg-background fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg sm:rounded-lg md:w-full",_()))));Ca(I,Ie({get transition(){return z()},get transitionConfig(){return f()},get class(){return t(T)}},()=>y,{children:(C,U)=>{var w=ja(),k=X(w);ee(k,e,"default",{},null);var O=oe(k,2);ga(O,{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none",children:(h,c)=>{var S=Na(),B=X(S);aa(B,{class:"h-4 w-4"}),St(2),s(h,S)},$$slots:{default:!0}}),s(C,w)},$$slots:{default:!0}})),s(a,P)},$$slots:{default:!0}}),we()}const Ma=fa,Ra=Da;var Va=H('<button aria-label="open navigation" class="p-2 text-sidebar-foreground hover:text-sidebar-primary"><!></button>'),Ha=(u,e)=>ge(e,!1),Ba=H("<li><a> </a></li>"),Ka=(u,e)=>ge(e,!1),La=H("<a> </a>"),Ua=(u,e)=>ge(e,!1),Wa=H('<div class="p-4 border-b-2 border-sidebar-border"><div class="flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <a href="/" class="text-lg font-black text-sidebar-foreground">Robynn.ai</a></div></div> <ul class="flex flex-col p-4 space-y-1"><!> <div class="flex-grow"></div> <li class="pt-4 border-t-2 border-sidebar-border"><div class="flex items-center justify-between px-3 py-2"><!> <a class="text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors">Sign Out</a></div></li></ul>',1),Ya=H("<!> <!>",1),qa=H('<a href="/" class="text-lg font-black text-sidebar-foreground">Robynn.ai</a>'),Ga=(u,e)=>ge(e,!t(e)),Ja=H('<span class="block text-center">🏠</span>'),Xa=H("<a><!></a>"),Qa=H("<a><!></a>"),Za=H(`<div class="text-sm bg-primary text-primary-foreground sticky px-4 py-3 text-center border-2 border-border shadow-brutal mb-6 font-bold">You're signed in as an anonymous user. <a href="/login/sign_up" class="underline font-bold hover:opacity-70">Sign Up to persist your changes</a></div>`),$a=H('<div class="grid grid-rows-[auto_1fr] lg:grid-rows-1 overflow-hidden top-0 bottom-0 right-0 left-0 absolute"><nav><div class="flex items-center space-x-2 inline lg:hidden"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <a href="/" class="text-lg font-black">Robynn.ai</a></div> <!> <ul class="hidden flex-col h-full lg:flex"><li class="mb-8"><div class="flex items-center justify-between"><div class="flex items-center space-x-2"><div class="w-8 h-8 flex items-center justify-center bg-sidebar-primary text-sidebar-primary-foreground border-2 border-sidebar-border shadow-brutal-sm"><span class="font-bold text-sm">R</span></div> <!></div> <button class="p-1.5 text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors rounded border-2 border-transparent hover:border-sidebar-border"><!></button></div></li> <nav class="space-y-1"></nav> <div class="flex-grow"></div> <div class="border-t-2 border-sidebar-border pt-4"><div><!> <a class="text-sm font-bold text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 transition-colors border-2 border-transparent p-1.5 rounded hover:border-sidebar-border"><!></a></div></div></ul></nav> <div class="px-6 lg:px-12 py-6 overflow-y-scroll relative bg-background min-h-full flex flex-col"><div class="flex-1"><!> <!></div> <div class="mt-auto"><!></div></div></div>');function Nn(u,e){Ce(e,!0);const[x,y]=Ae(),_=()=>xe(ea,"$page",x);let{session:z}=e.data,f=Ue(!1),a=Ue(!1);Le(()=>{if(typeof window<"u"){const n=localStorage.getItem("sidebar-collapsed");n!==null&&ge(a,n==="true")}}),Le(()=>{typeof window<"u"&&localStorage.setItem("sidebar-collapsed",String(t(a)))});const o="/dashboard/",P=ta();class Q{constructor(g,i,L){Ke(this,"href");Ke(this,"label");Ke(this,"active");this.href=g,this.label=i,this.active=L(this.href)}}let I=Ue(Dt([]));Le(()=>{var n;ge(I,[new Q(`${o}${(n=P.value)==null?void 0:n.slug}`,"Home",g=>_().url.pathname===g)],!0)});let T=Ue(void 0);Le(()=>{var n;ge(T,new Q(`${o}${(n=P.value)==null?void 0:n.slug}/settings`,"⚙️",g=>_().url.pathname.startsWith(g)),!0)});var C=$a(),U=F(C);let w;var k=oe(F(U),2);$e(k,()=>Ma,(n,g)=>{g(n,{get open(){return t(f)},set open(i){ge(f,i,!0)},children:(i,L)=>{var le=Ya(),j=X(le);$e(j,()=>Ra,(he,Je)=>{Je(he,{class:"lg:hidden",children:(Me,pt)=>{var Fe=Va(),Be=F(Fe);Zt(Be,{class:"h-5 w-5"}),A(Fe),s(Me,Fe)},$$slots:{default:!0}})});var de=oe(j,2);$e(de,()=>Ia,(he,Je)=>{Je(he,{transition:Me=>zt(Me,{x:300,duration:300}),class:"left-auto right-0 flex h-dvh max-h-screen w-full max-w-sm translate-x-1 flex-col overflow-y-scroll border-y-0 sm:rounded-none bg-sidebar",children:(Me,pt)=>{var Fe=Wa(),Be=oe(X(Fe),2),at=F(Be);st(at,17,()=>t(I),lt,(ke,_e)=>{let Xe=()=>t(_e).href,xt=()=>t(_e).label,Ct=()=>t(_e).active;var Qe=Ba(),Re=F(Qe);Re.__click=[Ha,f];var wt=F(Re,!0);A(Re),A(Qe),Oe(()=>{pe(Re,"href",Xe()),Ne(Re,1,`block w-full px-3 py-2 text-sm font-bold transition-colors border-2 ${Ct()?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),We(wt,xt())}),s(ke,Qe)});var nt=oe(at,4),rt=F(nt),ot=F(rt);{var yt=ke=>{var _e=La();_e.__click=[Ka,f];var Xe=F(_e,!0);A(_e),Oe(()=>{pe(_e,"href",t(T).href),Ne(_e,1,`text-sm font-bold transition-colors border-2 p-1 rounded ${t(T).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),We(Xe,t(T).label)}),s(ke,_e)};q(ot,ke=>{t(T)&&ke(yt)})}var it=oe(ot,2);it.__click=[Ua,f],A(rt),A(nt),A(Be),Oe(()=>{var ke;return pe(it,"href",`/dashboard/${((ke=P.value)==null?void 0:ke.slug)??""}/../../sign_out`)}),s(Me,Fe)},$$slots:{default:!0}})}),s(i,le)},$$slots:{default:!0}})});var O=oe(k,2),h=F(O),c=F(h),S=F(c),B=oe(F(S),2);{var ae=n=>{var g=qa();s(n,g)};q(B,n=>{t(a)||n(ae)})}A(S);var ne=oe(S,2);ne.__click=[Ga,a];var Ee=F(ne);{var be=n=>{ra(n,{class:"h-4 w-4"})},me=n=>{oa(n,{class:"h-4 w-4"})};q(Ee,n=>{t(a)?n(be):n(me,!1)})}A(ne),A(c),A(h);var r=oe(h,2);st(r,21,()=>t(I),lt,(n,g)=>{var i=Xa(),L=F(i);{var le=de=>{var he=Ja();s(de,he)},j=de=>{var he=Ze();Oe(()=>We(he,t(g).label)),s(de,he)};q(L,de=>{t(a)?de(le):de(j,!1)})}A(i),Oe(()=>{pe(i,"href",t(g).href),Ne(i,1,`block px-3 py-2 text-sm font-bold transition-colors border-2 ${t(g).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),pe(i,"title",t(a)?t(g).label:void 0)}),s(n,i)}),A(r);var N=oe(r,4),v=F(N);let G;var M=F(v);{var R=n=>{var g=Qa(),i=F(g);{var L=j=>{sa(j,{class:"h-4 w-4"})},le=j=>{var de=Ze();Oe(()=>We(de,t(T).label)),s(j,de)};q(i,j=>{t(a)?j(L):j(le,!1)})}A(g),Oe(()=>{pe(g,"href",t(T).href),Ne(g,1,`text-sm font-bold transition-colors border-2 p-1.5 rounded ${t(T).active?"bg-sidebar-primary text-sidebar-primary-foreground border-sidebar-border shadow-brutal-sm":"text-sidebar-foreground hover:text-sidebar-primary hover:bg-sidebar-accent hover:bg-opacity-20 border-transparent"}`),pe(g,"title",t(a)?"Settings":void 0)}),s(n,g)};q(M,n=>{t(T)&&n(R)})}var W=oe(M,2),m=F(W);{var Y=n=>{ia(n,{class:"h-4 w-4"})},V=n=>{var g=Ze("Sign Out");s(n,g)};q(m,n=>{t(a)?n(Y):n(V,!1)})}A(W),A(v),A(N),A(O),A(U);var re=oe(U,2),E=F(re),J=F(E);{var K=n=>{var g=Za();s(n,g)};q(J,n=>{z!=null&&z.user.is_anonymous&&n(K)})}var ie=oe(J,2);Ft(ie,()=>e.children),A(E);var D=oe(E,2),Z=F(D);Qt(Z,{}),A(D),A(re),A(C),Oe((n,g)=>{var i;Nt(C,`grid-template-columns: ${t(a)?"4.5rem":"18rem"} 1fr`),w=Ne(U,1,"w-full h-16 flex items-center justify-between lg:block lg:h-dvh p-4 bg-sidebar border-r-2 border-sidebar-border text-sidebar-foreground transition-all duration-300",null,w,n),pe(ne,"aria-label",t(a)?"Expand sidebar":"Collapse sidebar"),G=Ne(v,1,"flex items-center gap-2 px-3 py-2",null,G,g),pe(W,"href",`/dashboard/${((i=P.value)==null?void 0:i.slug)??""}/../../sign_out`),pe(W,"title",t(a)?"Sign Out":void 0)},[()=>({"lg:w-[4.5rem]":t(a),"lg:w-72":!t(a)}),()=>({"justify-center":t(a)})]),s(u,C),we(),y()}At(["click"]);export{Nn as component};

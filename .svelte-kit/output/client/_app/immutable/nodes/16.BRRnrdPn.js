import"../chunks/CWj6FrbW.js";import{f as m,s as l,e as d,a as p,$ as g,j as r,o as t}from"../chunks/wnqW1tdD.js";import{h as f}from"../chunks/CDPCzm7q.js";import{S as h}from"../chunks/BX8uenml.js";var v=m('<h1 class="text-2xl font-bold mb-6">Email Subscription</h1> <!>',1);function y(o,a){let{profile:e}=a.data,s=e==null?void 0:e.unsubscribed;var i=v();f(_=>{g.title="Change Email Subscription"});var n=l(d(i),2);const u=t(()=>s?"You are currently unsubscribed from emails":"You are currently subscribed to emails"),b=t(()=>s?"Re-subscribe":"Unsubscribe"),c=t(()=>s?"You have been re-subscribed to emails":"You have been unsubscribed from emails");h(n,{editable:!0,title:"Subscription",get message(){return r(u)},get saveButtonTitle(){return r(b)},get successBody(){return r(c)},formTarget:"/api?/toggleEmailSubscription",fields:[]}),p(o,i)}export{y as component};

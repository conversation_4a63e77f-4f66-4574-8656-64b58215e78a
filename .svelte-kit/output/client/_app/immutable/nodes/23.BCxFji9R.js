import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{o as Dr}from"../chunks/CfBaWyh2.js";import{b as Ne,e as qe,a as p,p as Nr,l as qr,h as Pr,f as b,t as D,j as e,g as jr,$ as Er,c as a,s as i,d as ze,i as l,k as g,m as te,r,n as R,aU as Fr,aM as ue}from"../chunks/wnqW1tdD.js";import{h as Lr,e as K,r as Ur,s as T}from"../chunks/CDPCzm7q.js";import{i as A}from"../chunks/BjRbZGyQ.js";import{e as ne,i as me}from"../chunks/CsnEE4l9.js";import{h as sr}from"../chunks/D8kkBG2G.js";import{c as Br}from"../chunks/ojdN50pv.js";import{b as Wr,s as ir,c as fe}from"../chunks/rh_XW2Tv.js";import{s as Or}from"../chunks/Bz0_kaay.js";import{t as De}from"../chunks/sBNKiCwy.js";import{b as Gr}from"../chunks/CJ-FD9ng.js";import{i as Hr}from"../chunks/BxG_UISn.js";import{a as Zr,s as nr}from"../chunks/D5ITLM2v.js";import{w as Yr}from"../chunks/BvpDAKCq.js";import{p as Jr}from"../chunks/idBKwYq8.js";import{s as lr,f as Kr}from"../chunks/CiB29Aqe.js";import{s as Pe}from"../chunks/BDqVm3Gq.js";import{l as je,s as Ee}from"../chunks/Cmdkv-7M.js";import{I as Fe}from"../chunks/CX_t0Ed_.js";import{Z as dr,C as cr,B as Vr}from"../chunks/Wm_FtPOB.js";import{T as Qr,a as Xr,C as ea,U as ra,B as vr,S as aa}from"../chunks/JZlAgLJx.js";import{C as pr}from"../chunks/ZAWXEYb0.js";import{D as ta,C as oa,a as sa}from"../chunks/D3xCqHYb.js";import{L as ia}from"../chunks/CDkJ1nAx.js";function na(U,M){const N=je(M,["children","$$slots","$$events","$$legacy"]),B=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"}],["path",{d:"M10 6h4"}],["path",{d:"M10 10h4"}],["path",{d:"M10 14h4"}],["path",{d:"M10 18h4"}]];Fe(U,Ee({name:"building-2"},()=>N,{get iconNode(){return B},children:(I,q)=>{var C=Ne(),m=qe(C);Pe(m,M,"default",{},null),p(I,C)},$$slots:{default:!0}}))}function la(U,M){const N=je(M,["children","$$slots","$$events","$$legacy"]),B=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"}],["circle",{cx:"12",cy:"12",r:"3"}]];Fe(U,Ee({name:"eye"},()=>N,{get iconNode(){return B},children:(I,q)=>{var C=Ne(),m=qe(C);Pe(m,M,"default",{},null),p(I,C)},$$slots:{default:!0}}))}function da(U,M){const N=je(M,["children","$$slots","$$events","$$legacy"]),B=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}]];Fe(U,Ee({name:"message-square"},()=>N,{get iconNode(){return B},children:(I,q)=>{var C=Ne(),m=qe(C);Pe(m,M,"default",{},null),p(I,C)},$$slots:{default:!0}}))}var ca=b('<button class="template-card p-6 border-2 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg text-left group" style="background: var(--card); border-color: var(--border); border-radius: 0.5rem;"><div class="mb-3"><!></div> <h4 class="text-lg font-bold mb-2 group-hover:text-primary transition-colors" style="color: var(--foreground);"> </h4> <p class="text-sm leading-relaxed" style="color: var(--muted-foreground);"> </p> <div class="flex items-center gap-1 mt-3 text-primary opacity-0 group-hover:opacity-100 transition-opacity"><span class="text-xs font-bold">Use Template</span> <!></div></button>'),va=b(`<div class="text-center py-8"><div class="flex items-center justify-center gap-2 mb-6"><!> <h3 class="text-2xl font-bold" style="color: var(--foreground);">Ready to Research</h3></div> <p class="font-medium mb-8 max-w-2xl mx-auto" style="color: var(--muted-foreground);">Get marketing intelligence on any company. Choose a template
                  below or ask your custom question.</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto"></div></div>`),pa=b('<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1" title="Download as Markdown"><!> Download</button>'),ua=b('<div class="p-4 border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium" style="color: var(--primary-foreground);"> </p></div>'),ma=b('<div class="p-4 border-b-2 border-border bg-muted/50"><div class="flex items-start gap-2"><span class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground rounded">TL;DR</span> <div class="text-sm font-medium formatted-summary" style="color: var(--foreground);"><!></div></div></div>'),fa=b('<span class="text-xs font-bold px-2 py-1 border border-border rounded" style="background: var(--accent); color: var(--accent-foreground);"> </span>'),ga=b('<div class="px-6 pt-4 pb-2"><div class="flex flex-wrap gap-2"></div></div>'),ba=b('<div class="border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;"><!> <!> <div class="p-6"><div class="formatted-content max-w-none"><!></div></div> <div class="px-6 pb-4 border-t border-border"><div class="flex flex-wrap gap-2 mt-4"><button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"><!> Compare with competitor</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"><!> Add visuals</button> <button class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"><!> Turn into slides</button></div></div></div>'),ha=b('<div><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"><!></div> <div class="flex-1 max-w-3xl"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1"><!> <span class="text-xs" style="color: var(--muted-foreground);"> </span></div> <!></div> <!></div></div>'),ya=b("<div><!></div>"),xa=b('<div class="flex items-start gap-3"><div class="flex-shrink-0 mt-0.5"><!></div> <div class="flex-1"><h4 class="text-sm font-bold mb-1"> </h4> <p class="text-xs"> </p></div></div>'),_a=b('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><h4 class="font-bold mb-4" style="color: var(--foreground);">Research Progress</h4> <div class="mb-6"><div class="flex items-center justify-between mb-2"><span class="text-sm font-medium" style="color: var(--foreground);">Overall Progress</span> <span class="text-sm font-bold" style="color: var(--primary);"> </span></div> <div class="w-full h-2 border-2 overflow-hidden" style="background: var(--muted); border-color: var(--border);"><div class="h-full transition-all duration-500 ease-out"></div></div></div> <div class="space-y-4"></div></div>'),wa=b('<div class="p-4 border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"><div class="flex items-center space-x-2"><div class="w-2 h-2 rounded-full animate-pulse" style="background: var(--primary);"></div> <div class="w-2 h-2 rounded-full animate-pulse animation-delay-2000" style="background: var(--primary);"></div> <div class="w-2 h-2 rounded-full animate-pulse animation-delay-4000" style="background: var(--primary);"></div> <span class="text-sm font-medium" style="color: var(--muted-foreground);">Initializing research process...</span></div></div>'),ka=b('<div class="flex gap-4"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2" style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="flex-1"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);">Athena</span> <span class="text-xs" style="color: var(--muted-foreground);">Conducting comprehensive research...</span></div> <!></div></div>'),$a=b("<option> </option>"),Ca=b('<div class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>'),Sa=b('<div class="h-screen flex flex-col" style="background: var(--background);"><div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="w-12 h-12 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div><h1 class="text-3xl font-black flex items-center gap-3" style="color: var(--foreground);"><!> Athena</h1> <p class="text-lg font-medium" style="color: var(--muted-foreground);">Your AI market researcher</p></div></div> <div class="flex items-center space-x-4"><div class="flex items-center space-x-2 px-4 py-2 border-2" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"><!> <span class="text-sm font-bold" style="color: var(--accent-foreground);">Real-time Data</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground"><a class="hover:text-foreground transition-colors">Dashboard</a> <!> <span class="text-foreground font-medium">Athena</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"><div class="h-full"><div class="card-brutal p-0 chat-container h-full" style="background: var(--card);"><div class="messages-wrapper messages-container"><div class="space-y-6"><!> <!> <!></div></div> <div class="input-wrapper p-6"><div class="flex items-center gap-2 mb-4"><span class="text-sm font-bold" style="color: var(--muted-foreground);">Format:</span> <select class="px-3 py-1 text-sm border-2 border-border bg-card text-foreground font-medium rounded" style="border-radius: 0.375rem;"></select></div> <div class="relative"><div class="spotlight-input-container"><textarea class="spotlight-input flex-1 resize-none min-h-[120px] p-6 pr-32 text-lg"></textarea> <button class="spotlight-button"><!> <span class="font-bold">Research</span></button></div></div></div></div></div></div></div>');function Xa(U,M){Nr(M,!1);const[N,B]=Zr(),I=()=>nr(Jr,"$page",N),q=()=>nr(C,"$messages",N),C=Yr([]);let m=te(""),P=te(!1),ge=0,be=te(""),j=te("executive"),W=te([]),oe=te(0);const he=["Compare Drift and Intercom's messaging and channel mix based on the last 30 days...","How is Adobe marketing Firefly across its channels based on recent data...","What influencer or social campaigns has Notion run recently...","Map Figma's demand-gen strategy from 2022 to now...","Analyze Stripe's developer marketing evolution over the past quarter..."],ur=[{icon:cr,title:"Company Snapshot",description:"Get a summary of performance, team, and competitors",prompt:"Provide a comprehensive company snapshot for [Company Name], including recent financial performance, leadership team overview, main competitors, and key business metrics."},{icon:Xr,title:"Go-to-Market Audit",description:"Evaluate positioning, messaging, channels, and campaigns",prompt:"Analyze [Company Name]'s go-to-market strategy including their positioning, messaging, marketing channels, recent campaigns, and overall effectiveness in reaching their target audience."},{icon:Vr,title:"Brand Perception & Category",description:"Analyze how a brand is perceived in its space",prompt:"Research [Company Name]'s brand perception, category positioning, competitive differentiation, and customer sentiment. Include analysis of their brand identity and market perception."}],mr=[{value:"executive",label:"Executive Summary",description:"Bullet points + key insights"},{value:"slide-ready",label:"Slide-ready",description:"Sections + headers for export"},{value:"battlecard",label:"Competitive Battlecard",description:"Strategic comparison format"}];function ye(){return Math.random().toString(36).substr(2,9)}async function Le(){var s;if(!e(m).trim()||e(P))return;let o="";e(j)==="executive"?o="[Executive Summary Format] Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly. Use clear sections and numbered citations. ":e(j)==="slide-ready"?o="[Slide-ready Format] Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides. Include numbered citations for all claims. ":e(j)==="battlecard"&&(o="[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons. Include numbered citations and competitive analysis. ");const t=o+e(m).trim();g(m,""),g(P,!0),g(W,[{id:1,title:"Initial Analysis",description:"Analyzing research request...",status:"pending"},{id:2,title:"Web Search",description:"Conducting comprehensive search...",status:"pending"},{id:3,title:"Financial Analysis",description:"Gathering financial data...",status:"pending"},{id:4,title:"Market Research",description:"Analyzing market position...",status:"pending"},{id:5,title:"Report Generation",description:"Creating research report...",status:"pending"}]),g(oe,0),C.update(n=>[...n,{id:ye(),role:"user",content:t,timestamp:new Date}]);try{const n=await fetch(`/dashboard/${I().params.envSlug}/researcher?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:t})});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const d=(s=n.body)==null?void 0:s.getReader(),h=new TextDecoder;if(d)for(;;){const{done:y,value:x}=await d.read();if(y)break;const k=h.decode(x).split(`
`);for(const S of k)if(S.startsWith("data: "))try{const v=JSON.parse(S.slice(6));if(v.type==="final_response"){const f=ye();C.update(E=>[...E,{id:f,role:"assistant",content:v.response,timestamp:new Date,isReport:!0}])}else v.step&&(g(oe,v.progress),g(W,e(W).map(f=>f.id===v.step?{...f,status:v.status,description:v.action}:f.id<v.step?{...f,status:"completed"}:f)))}catch(v){console.error("Error parsing SSE data:",v)}}}catch(n){console.error("Error sending message:",n),C.update(d=>[...d,{id:ye(),role:"assistant",content:"I apologize, but Athena encountered an error while processing your request. Please try again.",timestamp:new Date}])}finally{g(P,!1),g(W,[]),g(oe,0)}}function fr(o){o.key==="Enter"&&!o.shiftKey&&(o.preventDefault(),Le())}function gr(o){g(m,o.prompt);const t=document.querySelector("textarea");t&&t.focus()}function br(o){const t=o.split(`
`).filter(h=>h.trim()),s=t.slice(0,2).join(" ").substring(0,200)+"...",n=t.filter(h=>h.includes("key")||h.includes("important")||h.includes("significant")).slice(0,3),d=[];return(o.includes("growth")||o.includes("increase"))&&d.push("↑ Trending"),(o.includes("insight")||o.includes("analysis"))&&d.push("💡 Insight"),(o.includes("challenge")||o.includes("weakness"))&&d.push("⚠ Weakness"),{summary:s,insights:n,badges:d}}Dr(()=>{g(be,he[0]);const o=setInterval(()=>{ge=(ge+1)%he.length,g(be,he[ge])},4e3);return()=>clearInterval(o)});function hr(o){const t=yr(o.content),s=o.timestamp.toISOString().split("T")[0],n=`${t||"research-report"}-${s}.md`,d=`# Marketing Research Report by Athena
**Generated on:** ${o.timestamp.toLocaleDateString()}
**Time:** ${o.timestamp.toLocaleTimeString()}
**Format:** ${e(j).charAt(0).toUpperCase()+e(j).slice(1)}

---

${o.content}

---

*Report generated by Athena - Your AI Market Researcher*
`,h=new Blob([d],{type:"text/markdown"}),y=URL.createObjectURL(h),x=document.createElement("a");x.href=y,x.download=n,x.click(),URL.revokeObjectURL(y)}function yr(o){var s;const t=o.split(`
`);for(const n of t.slice(0,5)){if(n.includes("Company:")||n.includes("Company Name:"))return((s=n.split(":")[1])==null?void 0:s.trim().replace(/[^a-zA-Z0-9-]/g,""))||"";if(n.startsWith("# ")&&!n.includes("Research")&&!n.includes("Report"))return n.replace("# ","").trim().replace(/[^a-zA-Z0-9-]/g,"")||""}return""}function Ue(o){let t=o.replace(/^# (.*$)/gim,'<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>').replace(/^## (.*$)/gim,'<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>').replace(/^### (.*$)/gim,'<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>').replace(/^#### (.*$)/gim,'<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/\*(.*?)\*/g,'<em class="italic" style="color: var(--muted-foreground);">$1</em>').replace(/```([\s\S]*?)```/g,'<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>').replace(/`([^`]+)`/g,'<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>').replace(/\|(.+)\|/g,s=>"<tr>"+s.split("|").filter(d=>d.trim()).map(d=>d.trim()).map(d=>`<td class="border border-border px-3 py-2" style="color: var(--foreground);">${d}</td>`).join("")+"</tr>").replace(/^[\s]*[-*+] (.+)$/gim,'<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>').replace(/^[\s]*(\d+)\.\s+(.+)$/gim,'<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: decimal;"><span class="font-medium">$1.</span> $2</li>').replace(/^> (.+)$/gim,'<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>').replace(/\[(\d+)\]/g,'<sup class="citation-number bg-primary text-primary-foreground px-1 py-0.5 rounded text-xs font-bold ml-1">[$1]</sup>').replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>').replace(/\n\n/g,'</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">').replace(/\n/g,"<br>");return!t.startsWith("<h")&&!t.startsWith("<p")&&!t.startsWith("<ul")&&!t.startsWith("<ol")&&!t.startsWith("<blockquote")&&(t='<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">'+t+"</p>"),t=t.replace(/(<li[^>]*>.*?<\/li>)/gs,s=>s.includes("list-style-type: disc")?'<ul class="mb-4">'+s+"</ul>":s.includes("list-style-type: decimal")?'<ol class="mb-4">'+s+"</ol>":s),t.includes("<tr>")&&(t=t.replace(/(<tr>.*?<\/tr>)/gs,'<table class="w-full border-collapse border border-border my-4">$1</table>')),t}qr(()=>q(),()=>{q().length>0&&setTimeout(()=>{const o=document.querySelector(".messages-container");o&&(o.scrollTop=o.scrollHeight)},100)}),Pr(),Hr();var xe=Sa();Lr(o=>{Er.title="Athena - AI Market Researcher"});var _e=a(xe),Be=a(_e),We=a(Be),we=a(We),ke=a(we),xr=a(ke);na(xr,{class:"w-6 h-6",style:"color: var(--primary-foreground);"}),r(ke);var Oe=i(ke,2),Ge=a(Oe),_r=a(Ge);dr(_r,{class:"w-8 h-8",style:"color: var(--primary);"}),R(),r(Ge),R(2),r(Oe),r(we);var He=i(we,2),Ze=a(He),wr=a(Ze);Qr(wr,{class:"w-4 h-4",style:"color: var(--accent-foreground);"}),R(2),r(Ze),r(He),r(We),r(Be),r(_e);var $e=i(_e,2),Ye=a($e),Je=a(Ye),kr=i(Je,2);pr(kr,{class:"w-4 h-4"}),R(2),r(Ye),r($e);var Ke=i($e,2),Ve=a(Ke),Qe=a(Ve),Ce=a(Qe),Xe=a(Ce),er=a(Xe);{var $r=o=>{var t=va(),s=a(t),n=a(s);dr(n,{class:"w-6 h-6",style:"color: var(--primary);"}),R(2),r(s);var d=i(s,4);ne(d,5,()=>ur,me,(h,y)=>{var x=ca(),_=a(x),k=a(_);Br(k,()=>e(y).icon,(Q,G)=>{G(Q,{class:"w-8 h-8",style:"color: var(--primary);"})}),r(_);var S=i(_,2),v=a(S,!0);r(S);var f=i(S,2),E=a(f,!0);r(f);var O=i(f,2),se=i(a(O),2);pr(se,{class:"w-3 h-3"}),r(O),r(x),D(()=>{T(v,(e(y),l(()=>e(y).title))),T(E,(e(y),l(()=>e(y).description)))}),K("click",x,()=>gr(e(y))),p(h,x)}),r(d),r(t),p(o,t)};A(er,o=>{q(),l(()=>q().length===0)&&o($r)})}var rr=i(er,2);ne(rr,1,q,me,(o,t)=>{var s=ha(),n=a(s),d=a(n);{var h=c=>{ra(c,{class:"w-5 h-5",style:"color: var(--primary-foreground);"})},y=c=>{vr(c,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"})};A(d,c=>{e(t),l(()=>e(t).role==="user")?c(h):c(y,!1)})}r(n);var x=i(n,2),_=a(x),k=a(_),S=a(k,!0);r(k);var v=i(k,2),f=a(v);ea(f,{class:"w-3 h-3",style:"color: var(--muted-foreground);"});var E=i(f,2),O=a(E,!0);r(E),r(v);var se=i(v,2);{var Q=c=>{var $=pa(),w=a($);ta(w,{class:"w-3 h-3"}),R(),r($),K("click",$,()=>hr(e(t))),p(c,$)};A(se,c=>{e(t),l(()=>e(t).role==="assistant"&&e(t).isReport)&&c(Q)})}r(_);var G=i(_,2);{var u=c=>{var $=ua(),w=a($),ee=a(w,!0);r(w),r($),D(()=>T(ee,(e(t),l(()=>e(t).content)))),p(c,$)},X=c=>{var $=ba();const w=ze(()=>(e(t),l(()=>br(e(t).content))));var ee=a($);{var de=Y=>{var J=ma(),ie=a(J),pe=i(a(ie),2),Te=a(pe);sr(Te,()=>(ue(e(w)),l(()=>Ue(e(w).summary)))),r(pe),r(ie),r(J),p(Y,J)};A(ee,Y=>{ue(e(w)),l(()=>e(w).summary)&&Y(de)})}var H=i(ee,2);{var Me=Y=>{var J=ga(),ie=a(J);ne(ie,5,()=>(ue(e(w)),l(()=>e(w).badges)),me,(pe,Te)=>{var Ie=fa(),zr=a(Ie,!0);r(Ie),D(()=>T(zr,e(Te))),p(pe,Ie)}),r(ie),r(J),p(Y,J)};A(H,Y=>{ue(e(w)),l(()=>e(w).badges.length>0)&&Y(Me)})}var Z=i(H,2),ce=a(Z),F=a(ce);sr(F,()=>(e(t),l(()=>Ue(e(t).content)))),r(ce),r(Z);var z=i(Z,2),re=a(z),ae=a(re),L=a(ae);la(L,{class:"w-3 h-3"}),R(),r(ae);var ve=i(ae,2),Tr=a(ve);cr(Tr,{class:"w-3 h-3"}),R(),r(ve);var Re=i(ve,2),Ir=a(Re);da(Ir,{class:"w-3 h-3"}),R(),r(Re),r(re),r(z),r($),K("click",ae,()=>g(m,"Compare this analysis with their main competitor")),K("click",ve,()=>g(m,"Add visual charts and graphs to this analysis")),K("click",Re,()=>g(m,"Turn this analysis into presentation slides")),p(c,$)};A(G,c=>{e(t),l(()=>e(t).role==="user")?c(u):c(X,!1)})}r(x),r(s),D(c=>{Or(s,1,`flex gap-4 ${e(t),l(()=>e(t).role==="user"?"flex-row-reverse":"")??""}`),fe(n,`background: var(--${e(t),l(()=>e(t).role==="user"?"primary":"secondary")??""}); border-color: var(--border); box-shadow: var(--shadow-sm);`),T(S,(e(t),l(()=>e(t).role==="user"?"You":"Athena"))),T(O,c)},[()=>(e(t),l(()=>e(t).timestamp.toLocaleTimeString()))],ze),p(o,s)});var Cr=i(rr,2);{var Sr=o=>{var t=ka(),s=a(t),n=a(s);vr(n,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"}),r(s);var d=i(s,2),h=i(a(d),2);{var y=_=>{var k=_a(),S=i(a(k),2),v=a(S),f=i(a(v),2),E=a(f);r(f),r(v);var O=i(v,2),se=a(O);r(O),r(S);var Q=i(S,2);ne(Q,5,()=>e(W),G=>G.id,(G,u)=>{var X=xa(),c=a(X),$=a(c);{var w=F=>{var z=ya(),re=a(z);oa(re,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),r(z),De(3,z,()=>Kr,()=>({duration:200})),p(F,z)},ee=(F,z)=>{{var re=L=>{ia(L,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},ae=L=>{sa(L,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};A(F,L=>{e(u),l(()=>e(u).status==="active")?L(re):L(ae,!1)},z)}};A($,F=>{e(u),l(()=>e(u).status==="completed")?F(w):F(ee,!1)})}r(c);var de=i(c,2),H=a(de),Me=a(H,!0);r(H);var Z=i(H,2),ce=a(Z,!0);r(Z),r(de),r(X),D(()=>{fe(H,`color: ${e(u),l(()=>e(u).status==="pending"?"var(--muted-foreground)":"var(--foreground)")??""};${e(u),l(()=>e(u).status==="pending"?"opacity: 0.5":"")??""}`),T(Me,(e(u),l(()=>e(u).title))),fe(Z,`color: ${e(u),l(()=>(e(u).status==="pending","var(--muted-foreground)"))??""};${e(u),l(()=>e(u).status==="pending"?"opacity: 0.5":"")??""}`),T(ce,(e(u),l(()=>e(u).description)))}),De(3,X,()=>lr,()=>({duration:300})),p(G,X)}),r(Q),r(k),D(()=>{T(E,`${e(oe)??""}%`),fe(se,`background: var(--primary); width: ${e(oe)??""}%;`)}),De(3,k,()=>lr,()=>({duration:300})),p(_,k)},x=_=>{var k=wa();p(_,k)};A(h,_=>{e(W),l(()=>e(W).length>0)?_(y):_(x,!1)})}r(d),r(t),p(o,t)};A(Cr,o=>{e(P)&&o(Sr)})}r(Xe),r(Ce);var ar=i(Ce,2),Se=a(ar),Ae=i(a(Se),2);D(()=>{e(j),Fr(()=>{})}),ne(Ae,5,()=>mr,me,(o,t)=>{var s=$a(),n=a(s,!0);r(s);var d={};D(()=>{T(n,(e(t),l(()=>e(t).label))),d!==(d=(e(t),l(()=>e(t).value)))&&(s.value=(s.__value=(e(t),l(()=>e(t).value)))??"")}),p(o,s)}),r(Ae),r(Se);var tr=i(Se,2),or=a(tr),V=a(or);Ur(V);var le=i(V,2),Ar=a(le);{var Mr=o=>{var t=Ca();p(o,t)},Rr=o=>{aa(o,{class:"w-5 h-5"})};A(Ar,o=>{e(P)?o(Mr):o(Rr,!1)})}R(2),r(le),r(or),r(tr),r(ar),r(Qe),r(Ve),r(Ke),r(xe),D(o=>{ir(Je,"href",`/dashboard/${I(),l(()=>I().params.envSlug)??""}`),ir(V,"placeholder",e(be)),V.disabled=e(P),le.disabled=o},[()=>(e(m),e(P),l(()=>!e(m).trim()||e(P)))],ze),Wr(Ae,()=>e(j),o=>g(j,o)),Gr(V,()=>e(m),o=>g(m,o)),K("keydown",V,fr),K("click",le,Le),p(U,xe),jr(),B()}export{Xa as component};

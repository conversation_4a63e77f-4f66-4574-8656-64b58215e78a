import"../chunks/CWj6FrbW.js";import"../chunks/Cvx8ZW61.js";import{o as ht}from"../chunks/CfBaWyh2.js";import{b as Tr,e as Ze,a as g,p as ot,f as _,c as t,r,n as B,s as o,t as N,j as e,J as Ae,o as Br,k as c,g as at,a7 as _t,l as wt,h as kt,$ as $t,m as ce,i as V,d as dt,aU as St}from"../chunks/wnqW1tdD.js";import{s as K,e as R,r as pt,h as Mt}from"../chunks/CDPCzm7q.js";import{i as z}from"../chunks/BjRbZGyQ.js";import{e as mr,i as Kr}from"../chunks/CsnEE4l9.js";import{h as mt}from"../chunks/D8kkBG2G.js";import{c as Ct}from"../chunks/ojdN50pv.js";import{r as fr,b as st,c as ye,s as rt}from"../chunks/rh_XW2Tv.js";import{s as ve}from"../chunks/Bz0_kaay.js";import{t as Yr}from"../chunks/sBNKiCwy.js";import{b as He}from"../chunks/CJ-FD9ng.js";import{i as At}from"../chunks/BxG_UISn.js";import{a as Dt,s as ct}from"../chunks/D5ITLM2v.js";import{w as Lt}from"../chunks/BvpDAKCq.js";import{p as Kt}from"../chunks/idBKwYq8.js";import{s as tt,f as ft}from"../chunks/CiB29Aqe.js";import{l as zr,s as Fr,p as _r}from"../chunks/Cmdkv-7M.js";import{S as gt}from"../chunks/DrB3LJpu.js";import{C as bt}from"../chunks/ZAWXEYb0.js";import{s as Ur}from"../chunks/BDqVm3Gq.js";import{I as Ir}from"../chunks/CX_t0Ed_.js";import{L as Jr}from"../chunks/CDkJ1nAx.js";import{a as Wr,T as qr,C as Et,U as Rt,B as vt,S as Ot}from"../chunks/JZlAgLJx.js";import{D as nt,C as yt,a as xt}from"../chunks/D3xCqHYb.js";import{X as jt}from"../chunks/BAo0SQ6-.js";function Tt(se,M){const x=zr(M,["children","$$slots","$$events","$$legacy"]),ne=[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"}]];Ir(se,Fr({name:"book-open"},()=>x,{get iconNode(){return ne},children:(G,Q)=>{var O=Tr(),D=Ze(O);Ur(D,M,"default",{},null),g(G,O)},$$slots:{default:!0}}))}function ut(se,M){const x=zr(M,["children","$$slots","$$events","$$legacy"]),ne=[["circle",{cx:"12",cy:"12",r:"10"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16"}]];Ir(se,Fr({name:"circle-alert"},()=>x,{get iconNode(){return ne},children:(G,Q)=>{var O=Tr(),D=Ze(O);Ur(D,M,"default",{},null),g(G,O)},$$slots:{default:!0}}))}function it(se,M){const x=zr(M,["children","$$slots","$$events","$$legacy"]),ne=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"}]];Ir(se,Fr({name:"copy"},()=>x,{get iconNode(){return ne},children:(G,Q)=>{var O=Tr(),D=Ze(O);Ur(D,M,"default",{},null),g(G,O)},$$slots:{default:!0}}))}function zt(se,M){const x=zr(M,["children","$$slots","$$events","$$legacy"]),ne=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4"}],["path",{d:"M12 18v-6"}],["path",{d:"m9 15 3 3 3-3"}]];Ir(se,Fr({name:"file-down"},()=>x,{get iconNode(){return ne},children:(G,Q)=>{var O=Tr(),D=Ze(O);Ur(D,M,"default",{},null),g(G,O)},$$slots:{default:!0}}))}function lt(se,M){const x=zr(M,["children","$$slots","$$events","$$legacy"]),ne=[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"}]];Ir(se,Fr({name:"filter"},()=>x,{get iconNode(){return ne},children:(G,Q)=>{var O=Tr(),D=Ze(O);Ur(D,M,"default",{},null),g(G,O)},$$slots:{default:!0}}))}function Ft(se,M){const x=zr(M,["children","$$slots","$$events","$$legacy"]),ne=[["path",{d:"M5 12h14"}],["path",{d:"M12 5v14"}]];Ir(se,Fr({name:"plus"},()=>x,{get iconNode(){return ne},children:(G,Q)=>{var O=Tr(),D=Ze(O);Ur(D,M,"default",{},null),g(G,O)},$$slots:{default:!0}}))}var Ut=_('<button class="p-3 border-2 text-left hover:border-primary transition-all" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between"><div><p class="font-medium text-sm" style="color: var(--foreground);"> </p> <p class="text-xs mt-1" style="color: var(--muted-foreground);"> </p></div> <!></div></button>'),It=_('<div class="mt-4 p-4 border-2 space-y-4" style="background: var(--muted); border-color: var(--border);"><div class="grid md:grid-cols-2 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Industry/Niche</label> <input placeholder="e.g., E-commerce, SaaS, Healthcare" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Location</label> <select class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>United States</option><option>United Kingdom</option><option>Canada</option><option>Australia</option><option>Global</option></select></div></div> <div class="grid md:grid-cols-3 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Min Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Difficulty</label> <input type="number" max="100" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div></div></div>'),Pt=_("<!> Discovering Keywords...",1),Nt=_("<!> Discover Niche Keywords",1),Gt=_('<tr class="border-b hover:bg-muted/50" style="border-color: var(--border);"><td class="py-2 px-3 text-sm" style="color: var(--foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="px-2 py-1 text-xs rounded"> </span></td><td class="py-2 px-3 text-sm text-center"><span class="text-xs"> </span></td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="font-medium" style="color: var(--primary);"> </span></td></tr>'),Vt=_('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between mb-4"><h4 class="font-bold" style="color: var(--foreground);"> </h4> <div class="flex gap-2"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button></div></div> <div class="overflow-x-auto"><table class="w-full"><thead><tr class="border-b" style="border-color: var(--border);"><th class="text-left py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Keyword</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Volume</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Difficulty</th><th class="text-center py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Competition</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">CPC</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Opportunity</th></tr></thead><tbody></tbody></table></div></div>'),Bt=_('<div class="space-y-6"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="w-10 h-10 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border);"><!></div> <div><h3 class="text-lg font-bold" style="color: var(--foreground);">Niche Keyword Discovery</h3> <p class="text-sm" style="color: var(--muted-foreground);">Find untapped long-tail keywords in your specific niche</p></div></div></div> <div class="grid md:grid-cols-2 gap-3"></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Seed Keywords (comma-separated, max 20)</label> <textarea placeholder="e.g., organic coffee, fair trade coffee beans, specialty coffee roasters" class="w-full p-3 border-2 min-h-[80px]" style="background: var(--background); border-color: var(--border); color: var(--foreground);"></textarea></div> <div><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Advanced Filters <span>▼</span></button> <!></div> <button class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"><!></button> <!></div>');function Yt(se,M){ot(M,!0);let x=_r(M,"isLoading",3,!1),ne=_r(M,"discoveredKeywords",19,()=>[]),G=Ae(""),Q=Ae(""),O=Ae("United States"),D=Ae(100),C=Ae(1e4),X=Ae(50),ue=Ae(!1);const qe=[{name:"E-commerce",seeds:"organic skincare, natural beauty products, vegan cosmetics"},{name:"SaaS",seeds:"project management, team collaboration, task tracking"},{name:"Local Business",seeds:"coffee shop, specialty coffee, local cafe"},{name:"Health & Wellness",seeds:"yoga classes, meditation app, wellness coaching"}];function Je(){const f=e(G).split(",").map(E=>E.trim()).filter(E=>E.length>0);if(f.length===0)return;const A={industry:e(Q),location:e(O),volumeRange:{min:e(D),max:e(C)},maxDifficulty:e(X)};M.onAnalyze(f,A)}function ee(f){c(G,f,!0)}function je(){const f=e(Se).map(A=>A.keyword).join(`
`);navigator.clipboard.writeText(f)}function gr(){const f=["Keyword","Search Volume","Difficulty","Competition","CPC","Opportunity Score"],A=e(Se).map(U=>{var ie;return[U.keyword,U.search_volume,U.difficulty,U.competition,U.cpc.toFixed(2),((ie=U.opportunity_score)==null?void 0:ie.toFixed(2))||""]}),E=[f,...A].map(U=>U.join(",")).join(`
`),re=new Blob([E],{type:"text/csv"}),oe=URL.createObjectURL(re),Z=document.createElement("a");Z.href=oe,Z.download=`niche-keywords-${new Date().toISOString().split("T")[0]}.csv`,Z.click(),URL.revokeObjectURL(oe)}const Te=Br(()=>ne().map(f=>({...f,opportunity_score:f.search_volume/(f.difficulty+1)*(f.competition==="LOW"?2:f.competition==="MEDIUM"?1:.5)}))),Se=Br(()=>e(Te).filter(f=>f.search_volume>=e(D)&&f.search_volume<=e(C)).filter(f=>f.difficulty<=e(X)).sort((f,A)=>(A.opportunity_score||0)-(f.opportunity_score||0)));var Pe=Bt(),te=t(Pe),wr=t(te),ir=t(wr),xe=t(ir);gt(xe,{class:"w-5 h-5",style:"color: var(--primary-foreground);"}),r(ir),B(2),r(wr),r(te);var Ne=o(te,2);mr(Ne,21,()=>qe,Kr,(f,A)=>{var E=Ut(),re=t(E),oe=t(re),Z=t(oe),U=t(Z,!0);r(Z);var ie=o(Z,2),Me=t(ie,!0);r(ie),r(oe);var Fe=o(oe,2);bt(Fe,{class:"w-4 h-4",style:"color: var(--muted-foreground);"}),r(re),r(E),N(()=>{E.disabled=x(),K(U,e(A).name),K(Me,e(A).seeds)}),R("click",E,()=>ee(e(A).seeds)),g(f,E)}),r(Ne);var ze=o(Ne,2),Ge=o(t(ze),2);pt(Ge),r(ze);var Qe=o(ze,2),Xe=t(Qe),er=t(Xe);lt(er,{class:"w-4 h-4"});var Ve=o(er,2);r(Xe);var kr=o(Xe,2);{var $r=f=>{var A=It(),E=t(A),re=t(E),oe=o(t(re),2);fr(oe),r(re);var Z=o(re,2),U=o(t(Z),2),ie=t(U);ie.value=ie.__value="United States";var Me=o(ie);Me.value=Me.__value="United Kingdom";var Fe=o(Me);Fe.value=Fe.__value="Canada";var pe=o(Fe);pe.value=pe.__value="Australia";var tr=o(pe);tr.value=tr.__value="Global",r(U),r(Z),r(E);var lr=o(E,2),or=t(lr),q=o(t(or),2);fr(q),r(or);var De=o(or,2),me=o(t(De),2);fr(me),r(De);var br=o(De,2),he=o(t(br),2);fr(he),r(br),r(lr),r(A),N(()=>{oe.disabled=x(),U.disabled=x(),q.disabled=x(),me.disabled=x(),he.disabled=x()}),He(oe,()=>e(Q),_e=>c(Q,_e)),st(U,()=>e(O),_e=>c(O,_e)),He(q,()=>e(D),_e=>c(D,_e)),He(me,()=>e(C),_e=>c(C,_e)),He(he,()=>e(X),_e=>c(X,_e)),g(f,A)};z(kr,f=>{e(ue)&&f($r)})}r(Qe);var rr=o(Qe,2),Er=t(rr);{var Rr=f=>{var A=Pt(),E=Ze(A);Jr(E,{class:"w-4 h-4 animate-spin"}),B(),g(f,A)},Pr=f=>{var A=Nt(),E=Ze(A);Wr(E,{class:"w-4 h-4"}),B(),g(f,A)};z(Er,f=>{x()?f(Rr):f(Pr,!1)})}r(rr);var Nr=o(rr,2);{var Sr=f=>{var A=Vt(),E=t(A),re=t(E),oe=t(re);r(re);var Z=o(re,2),U=t(Z),ie=t(U);it(ie,{class:"w-3 h-3"}),B(),r(U);var Me=o(U,2),Fe=t(Me);nt(Fe,{class:"w-3 h-3"}),B(),r(Me),r(Z),r(E);var pe=o(E,2),tr=t(pe),lr=o(t(tr));mr(lr,21,()=>e(Se),Kr,(or,q)=>{var De=Gt(),me=t(De),br=t(me,!0);r(me);var he=o(me),_e=t(he,!0);r(he);var dr=o(he),Mr=t(dr),Cr=t(Mr,!0);r(Mr),r(dr);var yr=o(dr),n=t(yr),u=t(n,!0);r(n),r(yr);var b=o(yr),j=t(b);r(b);var L=o(b),Y=t(L),a=t(Y,!0);r(Y),r(L),r(De),N((s,v,d)=>{K(br,e(q).keyword),K(_e,s),ye(Mr,`background: ${e(q).difficulty<30?"var(--primary)":e(q).difficulty<60?"#fbbf24":"#ef4444"}; color: white;`),K(Cr,e(q).difficulty),ye(n,`color: ${e(q).competition==="LOW"?"var(--primary)":e(q).competition==="MEDIUM"?"#fbbf24":"#ef4444"};`),K(u,e(q).competition),K(j,`$${v??""}`),K(a,d)},[()=>e(q).search_volume.toLocaleString(),()=>e(q).cpc.toFixed(2),()=>{var s;return((s=e(q).opportunity_score)==null?void 0:s.toFixed(1))||"-"}]),g(or,De)}),r(lr),r(tr),r(pe),r(A),N(()=>K(oe,`Discovered Keywords (${e(Se).length??""})`)),R("click",U,je),R("click",Me,gr),g(f,A)};z(Nr,f=>{e(Se).length>0&&f(Sr)})}r(Pe),N(f=>{Ge.disabled=x(),Xe.disabled=x(),ve(Ve,1,`text-xs ${e(ue)?"rotate-180":""} transition-transform`),rr.disabled=f},[()=>!e(G).trim()||x()]),He(Ge,()=>e(G),f=>c(G,f)),R("click",Xe,()=>c(ue,!e(ue))),R("click",rr,Je),g(se,Pe),at()}var Wt=_('<button class="btn-secondary p-3"><!></button>'),Ht=_('<div class="flex gap-2"><input class="flex-1 p-3 border-2" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/> <!></div>'),Zt=_('<button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Add Competitor</button>'),qt=_('<div class="mt-4 p-4 border-2 space-y-4" style="background: var(--muted); border-color: var(--border);"><div class="grid md:grid-cols-3 gap-4"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Location</label> <select class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>United States</option><option>United Kingdom</option><option>Canada</option><option>Australia</option><option>Global</option></select></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Min Volume</label> <input type="number" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Max Difficulty</label> <input type="number" max="100" class="w-full p-2 border-2 text-sm" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div></div></div>'),Jt=_("<!> Analyzing Competitor Gaps...",1),Qt=_("<!> Analyze Competitor Gaps",1),Xt=_("<div><!></div>"),eo=_('<div class="mt-2 h-1 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div>'),ro=_('<div class="flex items-start gap-3"><div class="flex-shrink-0 mt-0.5"><!></div> <div class="flex-1"><h5 class="text-sm font-bold mb-1"> </h5> <p class="text-xs"> </p> <!></div></div>'),to=_('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><h4 class="font-bold mb-4" style="color: var(--foreground);">Analyzing Competitor Gaps</h4> <div class="space-y-4"></div> <div class="mt-6 pt-4 border-t" style="border-color: var(--border);"><div class="flex items-center justify-between mb-2"><span class="text-xs font-medium" style="color: var(--muted-foreground);">Overall Progress</span> <span class="text-xs font-bold" style="color: var(--foreground);"> </span></div> <div class="h-2 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div></div></div>'),oo=_('<div class="flex items-center justify-between p-2 border" style="background: var(--background); border-color: var(--border);"><span class="text-sm font-medium" style="color: var(--foreground);"> </span> <div class="flex items-center gap-3 text-xs"><span style="color: var(--muted-foreground);"> </span> <span class="px-2 py-1 rounded" style="background: var(--primary); color: var(--primary-foreground);"> </span></div></div>'),ao=_('<div class="border-2 p-4" style="background: var(--accent); border-color: var(--border);"><div class="flex items-center gap-2 mb-3"><!> <h4 class="font-bold" style="color: var(--accent-foreground);">Quick Win Opportunities</h4></div> <div class="space-y-2"></div></div>'),so=_(`<div class="border-2 p-4 mb-4" style="background: #fef3c7; border-color: #f59e0b;"><div class="flex items-center gap-2 mb-2"><!> <h4 class="font-bold text-sm" style="color: #78350f;">Demo Data Notice</h4></div> <p class="text-sm" style="color: #78350f;">You're viewing sample data. To get real keyword analysis for your
        domains, please configure your DataForSEO API credentials in the
        environment settings.</p> <p class="text-xs mt-2" style="color: #78350f;">The keywords shown below are generic examples and not specific to your
        actual domain.</p></div>`),no=_('<span class="text-xs font-normal px-2 py-1 rounded ml-2" style="background: #fef3c7; color: #78350f;">DEMO DATA</span>'),io=_('<tr class="border-b hover:bg-muted/50" style="border-color: var(--border);"><td class="py-2 px-3 text-sm" style="color: var(--foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="px-2 py-1 text-xs rounded"> </span></td><td class="py-2 px-3 text-sm text-center"><span class="text-xs px-2 py-1 rounded"> </span></td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);"> </td><td class="py-2 px-3 text-sm text-right"><span class="font-medium" style="color: var(--primary);"> </span></td></tr>'),lo=_('<div class="border-2 p-6" style="background: var(--card); border-color: var(--border);"><div class="flex items-center justify-between mb-4"><h4 class="font-bold" style="color: var(--foreground);"> <!></h4> <div class="flex gap-2"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button></div></div> <div class="overflow-x-auto"><table class="w-full"><thead><tr class="border-b" style="border-color: var(--border);"><th class="text-left py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Keyword</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Volume</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Difficulty</th><th class="text-center py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Gap Type</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Competitor Pos.</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Your Pos.</th><th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">Opportunity</th></tr></thead><tbody></tbody></table></div></div>'),co=_('<div class="border-2 p-6 mt-6" style="background: var(--card); border-color: var(--border);"><h3 class="text-lg font-bold mb-4" style="color: var(--foreground);">📊 Complete SEO Strategy Analysis</h3> <div class="prose prose-sm max-w-none" style="color: var(--muted-foreground);"><div class="ai-response-content"><!></div></div></div>'),vo=_(`<div class="space-y-6"><div class="flex items-center justify-between"><div class="flex items-center gap-3"><div class="w-10 h-10 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border);"><!></div> <div><h3 class="text-lg font-bold" style="color: var(--foreground);">Competitor Gap Analysis</h3> <p class="text-sm" style="color: var(--muted-foreground);">Find keyword opportunities your competitors are ranking for</p></div></div></div> <div class="space-y-4"><div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Your Domain</label> <input placeholder="example.com" class="w-full p-3 border-2" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Competitor Domains (up to 3)</label> <div class="space-y-2"><!> <!></div></div></div> <div><label class="block text-sm font-medium mb-2" style="color: var(--foreground);">Analysis Type</label> <div class="grid md:grid-cols-3 gap-3"><button><p class="font-medium text-sm" style="color: var(--foreground);">Missing Keywords</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Keywords competitors rank for but you don't</p></button> <button><p class="font-medium text-sm" style="color: var(--foreground);">Lower Rankings</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Keywords where competitors outrank you</p></button> <button><p class="font-medium text-sm" style="color: var(--foreground);">All Gaps</p> <p class="text-xs mt-1" style="color: var(--muted-foreground);">Show all keyword opportunities</p></button></div></div> <div><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"><!> Advanced Filters <span>▼</span></button> <!></div> <button class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"><!></button> <!> <!> <!> <!> <!></div>`);function uo(se,M){ot(M,!0);let x=_r(M,"isLoading",3,!1),ne=_r(M,"gapKeywords",19,()=>[]),G=_r(M,"progressSteps",19,()=>[]),Q=_r(M,"currentProgress",3,0),O=_r(M,"isMockData",3,!1),D=_r(M,"aiResponse",3,""),C=Ae(""),X=Ae(_t([""])),ue=Ae("United States"),qe=Ae(500),Je=Ae(70),ee=Ae("all"),je=Ae(!1);function gr(){e(X).length<3&&c(X,[...e(X),""],!0)}function Te(n){c(X,e(X).filter((u,b)=>b!==n),!0)}function Se(){const n=e(X).filter(b=>b.trim().length>0);if(!e(C).trim()||n.length===0)return;const u={yourDomain:e(C).trim(),competitors:n,location:e(ue),minVolume:e(qe),maxDifficulty:e(Je),gapType:e(ee)};M.onAnalyze(u)}function Pe(){const n=e(xe).map(u=>u.keyword).join(`
`);navigator.clipboard.writeText(n)}function te(n){return n.replace(/^### (.*$)/gim,'<h3 class="text-lg font-bold mb-2 mt-4" style="color: var(--foreground);">$1</h3>').replace(/^## (.*$)/gim,'<h2 class="text-xl font-bold mb-3 mt-6" style="color: var(--foreground);">$1</h2>').replace(/^# (.*$)/gim,'<h1 class="text-2xl font-bold mb-4" style="color: var(--foreground);">$1</h1>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold" style="color: var(--foreground);">$1</strong>').replace(/^[\-\*•] (.*$)/gim,'<li class="ml-6 mb-1 list-disc">$1</li>').replace(/\|(.+)\|/g,(b,j)=>{const L=j.split("|").map(s=>s.trim()).filter(s=>s.length>0);return j.includes("---")?"":`<tr>${L.map(s=>`<td class="border px-3 py-2 text-sm" style="border-color: var(--border);">${s}</td>`).join("")}</tr>`}).replace(/(<tr>.*<\/tr>)/gs,'<table class="w-full border-collapse border mt-4 mb-4" style="border-color: var(--border);">$1</table>').replace(/\n/g,"<br>")}function wr(){const n=["Keyword","Search Volume","Difficulty","Competition","CPC","Competitor Position","Your Position","Gap Type","Opportunity Score"],u=e(xe).map(a=>{var s;return[a.keyword,a.search_volume,a.difficulty,a.competition,a.cpc.toFixed(2),a.competitor_position,a.your_position||"Not ranking",a.gap_type,((s=a.opportunity_score)==null?void 0:s.toFixed(2))||""]}),b=[n,...u].map(a=>a.join(",")).join(`
`),j=new Blob([b],{type:"text/csv"}),L=URL.createObjectURL(j),Y=document.createElement("a");Y.href=L,Y.download=`competitor-gap-analysis-${new Date().toISOString().split("T")[0]}.csv`,Y.click(),URL.revokeObjectURL(L)}const ir=Br(()=>ne().map(n=>({...n,opportunity_score:n.search_volume/(n.difficulty+1)*(n.gap_type==="missing"?2:1)}))),xe=Br(()=>e(ir).filter(n=>n.search_volume>=e(qe)).filter(n=>n.difficulty<=e(Je)).filter(n=>e(ee)==="all"||n.gap_type===e(ee)).sort((n,u)=>(u.opportunity_score||0)-(n.opportunity_score||0))),Ne=Br(()=>e(xe).filter(n=>n.search_volume>1e3&&n.difficulty<30).slice(0,5));var ze=vo(),Ge=t(ze),Qe=t(Ge),Xe=t(Qe),er=t(Xe);qr(er,{class:"w-5 h-5",style:"color: var(--primary-foreground);"}),r(Xe),B(2),r(Qe),r(Ge);var Ve=o(Ge,2),kr=t(Ve),$r=o(t(kr),2);fr($r),r(kr);var rr=o(kr,2),Er=o(t(rr),2),Rr=t(Er);mr(Rr,17,()=>e(X),Kr,(n,u,b)=>{var j=Ht(),L=t(j);fr(L),rt(L,"placeholder",`competitor${b+1}.com`);var Y=o(L,2);{var a=s=>{var v=Wt(),d=t(v);jt(d,{class:"w-4 h-4"}),r(v),N(()=>v.disabled=x()),R("click",v,()=>Te(b)),g(s,v)};z(Y,s=>{e(X).length>1&&s(a)})}r(j),N(()=>L.disabled=x()),He(L,()=>e(X)[b],s=>e(X)[b]=s),g(n,j)});var Pr=o(Rr,2);{var Nr=n=>{var u=Zt(),b=t(u);Ft(b,{class:"w-4 h-4"}),B(),r(u),N(()=>u.disabled=x()),R("click",u,gr),g(n,u)};z(Pr,n=>{e(X).length<3&&n(Nr)})}r(Er),r(rr),r(Ve);var Sr=o(Ve,2),f=o(t(Sr),2),A=t(f),E=o(A,2),re=o(E,2);r(f),r(Sr);var oe=o(Sr,2),Z=t(oe),U=t(Z);lt(U,{class:"w-4 h-4"});var ie=o(U,2);r(Z);var Me=o(Z,2);{var Fe=n=>{var u=qt(),b=t(u),j=t(b),L=o(t(j),2),Y=t(L);Y.value=Y.__value="United States";var a=o(Y);a.value=a.__value="United Kingdom";var s=o(a);s.value=s.__value="Canada";var v=o(s);v.value=v.__value="Australia";var d=o(v);d.value=d.__value="Global",r(L),r(j);var i=o(j,2),m=o(t(i),2);fr(m),r(i);var p=o(i,2),l=o(t(p),2);fr(l),r(p),r(b),r(u),N(()=>{L.disabled=x(),m.disabled=x(),l.disabled=x()}),st(L,()=>e(ue),y=>c(ue,y)),He(m,()=>e(qe),y=>c(qe,y)),He(l,()=>e(Je),y=>c(Je,y)),g(n,u)};z(Me,n=>{e(je)&&n(Fe)})}r(oe);var pe=o(oe,2),tr=t(pe);{var lr=n=>{var u=Jt(),b=Ze(u);Jr(b,{class:"w-4 h-4 animate-spin"}),B(),g(n,u)},or=n=>{var u=Qt(),b=Ze(u);Wr(b,{class:"w-4 h-4"}),B(),g(n,u)};z(tr,n=>{x()?n(lr):n(or,!1)})}r(pe);var q=o(pe,2);{var De=n=>{var u=to(),b=o(t(u),2);mr(b,21,G,d=>d.id,(d,i)=>{var m=ro(),p=t(m),l=t(p);{var y=h=>{var J=Xt(),de=t(J);yt(de,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),r(J),Yr(3,J,()=>ft,()=>({duration:200})),g(h,J)},I=(h,J)=>{{var de=Le=>{Jr(Le,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},ar=Le=>{xt(Le,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};z(h,Le=>{e(i).status==="active"?Le(de):Le(ar,!1)},J)}};z(l,h=>{e(i).status==="completed"?h(y):h(I,!1)})}r(p);var $=o(p,2),T=t($),w=t(T,!0);r(T);var S=o(T,2),W=t(S,!0);r(S);var fe=o(S,2);{var le=h=>{var J=eo(),de=t(J);r(J),N(()=>ye(de,`background: var(--primary); width: ${e(i).progress??""}%`)),g(h,J)};z(fe,h=>{e(i).status==="active"&&e(i).progress&&h(le)})}r($),r(m),N(()=>{ye(T,`color: ${e(i).status==="pending"?"var(--muted-foreground)":"var(--foreground)"};
                         opacity: ${e(i).status==="pending"?"0.5":"1"}`),K(w,e(i).title),ye(S,`color: var(--muted-foreground);
                        opacity: ${e(i).status==="pending"?"0.5":"1"}`),K(W,e(i).description)}),Yr(3,m,()=>tt,()=>({duration:300})),g(d,m)}),r(b);var j=o(b,2),L=t(j),Y=o(t(L),2),a=t(Y);r(Y),r(L);var s=o(L,2),v=t(s);r(s),r(j),r(u),N(()=>{K(a,`${Q()??""}%`),ye(v,`background: linear-gradient(to right, var(--primary), var(--accent)); 
                      width: ${Q()??""}%`)}),Yr(3,u,()=>tt,()=>({duration:300})),g(n,u)};z(q,n=>{x()&&G().length>0&&n(De)})}var me=o(q,2);{var br=n=>{var u=ao(),b=t(u),j=t(b);ut(j,{class:"w-5 h-5",style:"color: var(--accent-foreground);"}),B(2),r(b);var L=o(b,2);mr(L,21,()=>e(Ne),Kr,(Y,a)=>{var s=oo(),v=t(s),d=t(v,!0);r(v);var i=o(v,2),m=t(i),p=t(m);r(m);var l=o(m,2),y=t(l);r(l),r(i),r(s),N(I=>{K(d,e(a).keyword),K(p,`Vol: ${I??""}`),K(y,`Difficulty: ${e(a).difficulty??""}`)},[()=>e(a).search_volume.toLocaleString()]),g(Y,s)}),r(L),r(u),g(n,u)};z(me,n=>{e(Ne).length>0&&!x()&&n(br)})}var he=o(me,2);{var _e=n=>{var u=so(),b=t(u),j=t(b);ut(j,{class:"w-5 h-5",style:"color: #f59e0b;"}),B(2),r(b),B(4),r(u),g(n,u)};z(he,n=>{O()&&e(xe).length>0&&!x()&&n(_e)})}var dr=o(he,2);{var Mr=n=>{var u=lo(),b=t(u),j=t(b),L=t(j),Y=o(L);{var a=I=>{var $=no();g(I,$)};z(Y,I=>{O()&&I(a)})}r(j);var s=o(j,2),v=t(s),d=t(v);it(d,{class:"w-3 h-3"}),B(),r(v);var i=o(v,2),m=t(i);nt(m,{class:"w-3 h-3"}),B(),r(i),r(s),r(b);var p=o(b,2),l=t(p),y=o(t(l));mr(y,21,()=>e(xe),Kr,(I,$)=>{var T=io(),w=t(T),S=t(w,!0);r(w);var W=o(w),fe=t(W,!0);r(W);var le=o(W),h=t(le),J=t(h,!0);r(h),r(le);var de=o(le),ar=t(de),Le=t(ar,!0);r(ar),r(de);var Be=o(de),xr=t(Be);r(Be);var Ue=o(Be),Ar=t(Ue,!0);r(Ue);var Dr=o(Ue),Hr=t(Dr),Qr=t(Hr,!0);r(Hr),r(Dr),r(T),N((Or,P)=>{K(S,e($).keyword),K(fe,Or),ye(h,`background: ${e($).difficulty<30?"var(--primary)":e($).difficulty<60?"#fbbf24":"#ef4444"}; color: white;`),K(J,e($).difficulty),ye(ar,`background: ${e($).gap_type==="missing"?"#ef4444":"#fbbf24"}; color: white;`),K(Le,e($).gap_type==="missing"?"Not Ranking":"Lower Rank"),K(xr,`#${e($).competitor_position??""}`),K(Ar,e($).your_position?`#${e($).your_position}`:"-"),K(Qr,P)},[()=>e($).search_volume.toLocaleString(),()=>{var Or;return((Or=e($).opportunity_score)==null?void 0:Or.toFixed(1))||"-"}]),g(I,T)}),r(y),r(l),r(p),r(u),N(()=>K(L,`Gap Analysis Results (${e(xe).length??""}) `)),R("click",v,Pe),R("click",i,wr),g(n,u)};z(dr,n=>{e(xe).length>0&&!x()&&n(Mr)})}var Cr=o(dr,2);{var yr=n=>{var u=co(),b=o(t(u),2),j=t(b),L=t(j);mt(L,()=>te(D())),r(j),r(b),r(u),g(n,u)};z(Cr,n=>{D()&&D().trim().length>0&&!x()&&n(yr)})}r(ze),N(n=>{$r.disabled=x(),ve(A,1,`p-3 border-2 text-left ${e(ee)==="missing"?"border-primary":""}`),ye(A,`background: var(--card); border-color: ${e(ee)==="missing"?"var(--primary)":"var(--border)"};`),A.disabled=x(),ve(E,1,`p-3 border-2 text-left ${e(ee)==="lower_rank"?"border-primary":""}`),ye(E,`background: var(--card); border-color: ${e(ee)==="lower_rank"?"var(--primary)":"var(--border)"};`),E.disabled=x(),ve(re,1,`p-3 border-2 text-left ${e(ee)==="all"?"border-primary":""}`),ye(re,`background: var(--card); border-color: ${e(ee)==="all"?"var(--primary)":"var(--border)"};`),re.disabled=x(),Z.disabled=x(),ve(ie,1,`text-xs ${e(je)?"rotate-180":""} transition-transform`),pe.disabled=n},[()=>!e(C).trim()||e(X).filter(n=>n.trim()).length===0||x()]),He($r,()=>e(C),n=>c(C,n)),R("click",A,()=>c(ee,"missing")),R("click",E,()=>c(ee,"lower_rank")),R("click",re,()=>c(ee,"all")),R("click",Z,()=>c(je,!e(je))),R("click",pe,Se),g(se,ze),at()}var po=_('<button class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group" style="background: var(--card); border-color: var(--border);"><div class="flex items-center gap-3 mb-3"><div class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform" style="background: var(--primary); border-color: var(--border);"><!></div> <h4 class="font-bold text-sm" style="color: var(--foreground);"> </h4></div> <p class="text-xs mb-3" style="color: var(--muted-foreground);"> </p> <div class="text-xs font-mono p-2 border-2 rounded" style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);"> </div></button>'),mo=_('<div class="text-center py-12"><div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2" style="background: var(--muted); border-color: var(--border);"><!></div> <h3 class="text-xl font-bold mb-2" style="color: var(--foreground);">Start Your SEO Research</h3> <p class="font-medium mb-6" style="color: var(--muted-foreground);">Get keyword analysis and SEO strategy for any business</p> <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto"></div></div>'),fo=_('<button class="btn-secondary px-2 py-1 text-xs flex items-center gap-1" title="Download as Markdown"><!> Download</button>'),go=_('<div class="p-4 border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><p class="font-medium" style="color: var(--primary-foreground);"> </p></div>'),bo=_('<div class="p-6 border-2 mb-4" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"><div class="prose prose-sm max-w-none"><!></div></div> <div class="flex flex-wrap gap-2 mb-4"><button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Copy</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Export CSV</button> <button class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"><!> Generate Blog Outline</button></div>',1),yo=_('<div><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"><!></div> <div class="flex-1 max-w-3xl"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);"> </span> <div class="flex items-center gap-1"><!> <span class="text-xs" style="color: var(--muted-foreground);"> </span></div> <!></div> <!></div></div>'),xo=_("<div><!></div>"),ho=_('<div class="mt-2 h-1 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div>'),_o=_('<div class="flex items-start gap-3"><div class="flex-shrink-0 mt-0.5"><!></div> <div class="flex-1"><h4 class="text-sm font-bold mb-1"> </h4> <p class="text-xs"> </p> <!></div></div>'),wo=_('<div class="flex gap-4"><div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2" style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div class="flex-1"><div class="flex items-center gap-2 mb-2"><span class="text-sm font-bold" style="color: var(--foreground);">SEO Strategist</span> <span class="text-xs" style="color: var(--muted-foreground);">Working on your analysis...</span></div> <div class="p-6 border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"><div class="space-y-4"></div> <div class="mt-6 pt-4 border-t" style="border-color: var(--border);"><div class="flex items-center justify-between mb-2"><span class="text-xs font-medium" style="color: var(--muted-foreground);">Overall Progress</span> <span class="text-xs font-bold" style="color: var(--foreground);"> </span></div> <div class="h-2 rounded-full overflow-hidden" style="background: var(--muted);"><div class="h-full transition-all duration-500 ease-out"></div></div></div></div></div></div>'),ko=_('<div class="grid md:grid-cols-3 gap-4 p-4 border-2" style="background: var(--muted); border-color: var(--border);"><div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Target Audience</label> <select class="w-full p-2 border-2 text-xs" style="background: var(--background); border-color: var(--border); color: var(--foreground);"><option>Select audience</option><option>B2B SaaS</option><option>E-commerce</option><option>Local business</option><option>Content creators</option><option>Enterprise</option></select></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Region Focus</label> <input placeholder="e.g., US, Europe, Global" class="w-full p-2 border-2 text-xs" style="background: var(--background); border-color: var(--border); color: var(--foreground);"/></div> <div><label class="block text-xs font-bold mb-2" style="color: var(--foreground);">Funnel Stage</label> <div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Awareness</button> <button style="border-color: var(--border);">Consideration</button> <button>Decision</button></div></div></div>'),$o=_('<div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>'),So=_('<div class="h-full"><div class="card-brutal p-0 chat-container h-full" style="background: var(--card);"><div class="messages-wrapper messages-wrapper-seo messages-container"><div class="space-y-6"><!> <!> <!></div></div> <div><div class="flex items-center justify-between" style="margin-bottom: 15px;"><h2 class="text-lg font-bold" style="color: var(--foreground);">SEO Analysis</h2> <div class="flex items-center space-x-2"><span class="text-sm font-medium" style="color: var(--muted-foreground);">Output Format:</span> <div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Summary</button> <button style="border-color: var(--border);">Table</button> <button>Blog-ready</button></div></div></div> <div style="margin-bottom: 15px;"><button class="btn-secondary px-3 py-2 text-sm flex items-center gap-2" style="margin-bottom: 10px;"><!> Prompt Enhancer <span>▼</span></button> <!></div> <div class="flex" style="gap: 15px;"><div class="flex-1 relative"><textarea class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full" style="min-height: 60px;"></textarea></div> <button class="btn-primary px-6 font-bold flex items-center gap-2" style="height: auto; align-self: stretch;"><!> Analyze</button></div></div></div></div>'),Mo=_('<div class="h-full"><div class="card-brutal p-6 h-full overflow-y-auto" style="background: var(--card);"><!></div></div>'),Co=_('<div class="h-full"><div class="card-brutal p-6 h-full overflow-y-auto" style="background: var(--card);"><!></div></div>'),Ao=_('<div class="h-screen flex flex-col" style="background: var(--background);"><div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);"><div class="max-w-7xl mx-auto px-6 lg:px-8 py-6"><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><div class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"><!></div> <div><h1 class="text-3xl font-black" style="color: var(--foreground);">Lexi - SEO Strategist</h1> <p class="text-lg font-medium" style="color: var(--muted-foreground);">Powered by Lexi – your AI SEO Sidekick</p></div></div> <div class="flex items-center space-x-4"><div class="flex border-2" style="border-color: var(--border); background: var(--background);"><button>Chat Mode</button> <button style="border-color: var(--border);"><!> Niche Discovery</button> <button style="border-color: var(--border);"><!> Gap Analysis</button></div> <div class="flex items-center space-x-2 px-4 py-2 border-2" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"><!> <span class="text-sm font-bold" style="color: var(--accent-foreground);">Keyword Intelligence</span></div></div></div></div></div> <div class="max-w-7xl px-6 lg:px-8 py-4"><nav class="flex items-center space-x-2 text-sm text-muted-foreground"><a class="hover:text-foreground transition-colors">Dashboard</a> <!> <span class="text-foreground font-medium">SEO Agent</span></nav></div> <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"><!></div></div>');function ta(se,M){ot(M,!1);const[x,ne]=Dt(),G=()=>ct(Kt,"$page",x),Q=()=>ct(O,"$messages",x),O=Lt([]);let D=ce(""),C=ce(!1),X="",ue=ce("summary"),qe=0,Je=ce(""),ee=ce(!1),je=ce(""),gr=ce(""),Te=ce("awareness"),Se=ce([]),Pe=ce(0),te=ce("chat"),wr=ce([]),ir=ce([]),xe=ce([]),Ne=ce(0),ze=ce(!1),Ge=ce("");const Qe=["Find long-tail keywords for organic skincare...","Analyze competitor keywords for project management tools...","Discover niche keywords for sustainable fashion brands...","Research local SEO keywords for coffee shops in Seattle..."],Xe=[{icon:Ot,title:"Niche Keyword Discovery",description:"Find untapped long-tail keywords in your specific niche",prompt:"Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]"},{icon:qr,title:"Competitor Gap Analysis",description:"Identify keyword opportunities your competitors are missing",prompt:"Analyze keyword gaps between [Your Business] and competitors like [Competitor Names] in the [Industry] space"}];function er(){return Math.random().toString(36).substr(2,9)}async function Ve(){var d;if(!e(D).trim()||e(C))return;let a=e(D).trim();const s=[];e(je)&&s.push(`Target audience: ${e(je)}`),e(gr)&&s.push(`Region focus: ${e(gr)}`),e(Te)&&s.push(`Funnel stage: ${e(Te)}`),s.length>0&&(a=`${a}

Additional context: ${s.join(", ")}`),a+=`

Output format: ${e(ue)}`;const v=a;er(),c(D,""),c(C,!0),c(Se,[{id:1,title:"Industry Research",description:"Analyzing your business niche...",status:"pending"},{id:2,title:"Keyword Discovery",description:"Finding relevant keywords...",status:"pending"},{id:3,title:"Volume Analysis",description:"Checking search volumes...",status:"pending"},{id:4,title:"Competition Analysis",description:"Analyzing keyword difficulty...",status:"pending"},{id:5,title:"Report Generation",description:"Creating your SEO strategy...",status:"pending"}]),c(Pe,0),O.update(i=>[...i,{id:er(),role:"user",content:v,timestamp:new Date}]);try{const i=await fetch(`/dashboard/${G().params.envSlug}/agent-seo?stream=true`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:v})});if(!i.ok)throw new Error(`HTTP error! status: ${i.status}`);const m=(d=i.body)==null?void 0:d.getReader(),p=new TextDecoder;if(!m)throw new Error("No response body");for(;;){const{done:l,value:y}=await m.read();if(l)break;const $=p.decode(y).split(`
`);for(const T of $)if(T.startsWith("data: "))try{const w=JSON.parse(T.slice(6));if(w.type==="final"){const S=er();if(O.update(W=>[...W,{id:S,role:"assistant",content:w.response,timestamp:new Date,isReport:!0}]),X=S,e(te)==="gap"){const W=Nr(w.response);W.length>0&&c(ir,[...W]),c(Ge,w.response)}}else if(w.type==="error")if(console.error("❌ Agent error received:",w.error),w.error.includes("maximum context length")||w.error.includes("context window")){console.log("⚠️ Context limit reached - using fallback data generation"),c(ze,!0),e(te)==="gap"&&c(ir,E()),O.update(S=>[...S,{id:er(),role:"assistant",content:`⚠️ **Analysis successful but with limitations**

Your domains have extensive keyword data (found 500+ keywords each), which exceeded our processing capacity. I've generated a representative analysis based on the most relevant opportunities for your industry.

*Note: This represents a subset of potential opportunities. For complete analysis, try analyzing one competitor at a time.*`,timestamp:new Date,isReport:!0}]);break}else throw new Error(w.error);else w.step&&(c(Pe,w.progress),c(Se,e(Se).map(S=>S.id===w.step?{...S,status:w.status,description:w.action}:S.id<w.step?{...S,status:"completed"}:S)),e(te)==="gap"&&(c(Ne,w.progress),c(xe,e(xe).map(S=>S.id===w.step?{...S,status:w.status,description:w.action}:S.id<w.step?{...S,status:"completed"}:S))))}catch(w){console.error("Error parsing SSE data:",w)}}}catch(i){console.error("Error sending message:",i),O.update(m=>[...m,{id:er(),role:"assistant",content:"I apologize, but an error occurred while processing your request. Please try again.",timestamp:new Date}])}finally{c(C,!1),c(Se,[])}}function kr(a){a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),Ve())}function $r(a){const s=rr(a.content),v=a.timestamp.toISOString().split("T")[0],d=`${s||"seo-strategy"}-${v}.md`,i=`# SEO Strategy Report
**Generated on:** ${a.timestamp.toLocaleDateString()}
**Time:** ${a.timestamp.toLocaleTimeString()}

---

${a.content}

---

*Report generated by Robynn.ai SEO Strategist Agent*
`,m=new Blob([i],{type:"text/markdown"}),p=URL.createObjectURL(m),l=document.createElement("a");l.href=p,l.download=d,l.click(),URL.revokeObjectURL(p)}function rr(a){var v;const s=a.split(`
`);for(const d of s.slice(0,5)){if(d.includes("Business:")||d.includes("Company:"))return((v=d.split(":")[1])==null?void 0:v.trim().replace(/[^a-zA-Z0-9-]/g,""))||"";if(d.startsWith("# ")&&!d.includes("SEO")&&!d.includes("Strategy"))return d.replace("# ","").trim().replace(/[^a-zA-Z0-9-]/g,"")||""}return""}function Er(a){let s=a.replace(/^### (.*$)/gim,'<h3 class="text-lg font-bold text-foreground mb-2 mt-4">$1</h3>').replace(/^## (.*$)/gim,'<h2 class="text-xl font-bold text-foreground mb-3 mt-6">$1</h2>').replace(/^# (.*$)/gim,'<h1 class="text-2xl font-bold text-foreground mb-4">$1</h1>').replace(/\*\*(.*?)\*\*/g,'<strong class="font-bold text-foreground">$1</strong>').replace(/^[\-\*•] (.*$)/gim,'<li class="ml-6 mb-1 text-muted-foreground list-disc">$1</li>').replace(/^\d+\. (.*$)/gim,'<li class="ml-6 mb-1 text-muted-foreground list-decimal">$1</li>').replace(/```([\s\S]*?)```/g,'<pre class="bg-muted p-3 rounded my-3 overflow-x-auto"><code class="text-sm">$1</code></pre>').replace(/`([^`]+)`/g,'<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>').replace(/\|(.+)\|/g,d=>{const i=d.split("|").filter(p=>p.trim());return i.some(p=>p.trim().match(/^[\-:]+$/))?"":`<tr>${i.map(p=>`<td class="border border-border px-3 py-1">${p.trim()}</td>`).join("")}</tr>`});return s=s.replace(/(<li class="[^"]*list-disc[^"]*">[\s\S]*?<\/li>\s*)+/g,'<ul class="mb-4">$&</ul>'),s=s.replace(/(<li class="[^"]*list-decimal[^"]*">[\s\S]*?<\/li>\s*)+/g,'<ol class="mb-4">$&</ol>'),s=s.replace(/(<tr>[\s\S]*?<\/tr>\s*)+/g,'<table class="w-full mb-4 border-collapse">$&</table>'),s=s.split(`
`).map(d=>d.trim()&&!d.startsWith("<")?`<p class="text-muted-foreground leading-relaxed mb-3">${d}</p>`:d).join(`
`),s}ht(()=>{c(Je,Qe[0]);const a=setInterval(()=>{qe=(qe+1)%Qe.length,c(Je,Qe[qe])},3e3);return()=>clearInterval(a)});function Rr(a){navigator.clipboard.writeText(a)}function Pr(a){const s=new Blob([a],{type:"text/csv"}),v=URL.createObjectURL(s),d=document.createElement("a");d.href=v,d.download=`seo-keywords-${new Date().toISOString().split("T")[0]}.csv`,d.click(),URL.revokeObjectURL(v)}function Nr(a){const v=["Mock data returned","DataForSEO credentials not configured","mock data for testing","sample data","demo data","placeholder data","example data","generic examples","not specific to your actual domain","representative analysis","fallback data generation","context limit reached"].some(d=>a.toLowerCase().includes(d.toLowerCase()));if(c(ze,v),v)return[{keyword:"project management software",search_volume:12e3,difficulty:45,competition:"medium",cpc:8.5,competitor_position:3,your_position:null,gap_type:"missing",opportunity_score:85},{keyword:"best project management tools",search_volume:8500,difficulty:60,competition:"high",cpc:12.3,competitor_position:5,your_position:15,gap_type:"lower_rank",opportunity_score:72}];try{const d=[/\|\s*([^|]+?)\s*\|\s*(\d+)\s*\|\s*(\d+)\s*\|\s*(Missing|Lower)\s*\|\s*(\d+)\s*\|\s*([0-9.]+)\s*\|/g,/\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|/g,/\|\s*\d+\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|\s*([^|]+?)\s*\|/g,/([a-zA-Z][^,\n]+),\s*(\d+[,\d]*),\s*(\d+),\s*([^,\n]+),\s*([^,\n]+),\s*([0-9.]+)/g];let i=[];for(const l of d){const y=Array.from(a.matchAll(l));if(y.length>0&&(y.forEach((I,$)=>{let T,w,S,W,fe,le;l===d[0]?[,T,w,S,W,fe,le]=I:l===d[1]?[,T,w,S,W,fe,le]=I:l===d[2]?[,T,w,S,W,fe,le]=I:[,T,w,S,W,fe,le]=I;const h=T.toLowerCase().trim();if(h.includes("keyword")||h.includes("gap type")||h.includes("volume")||h.includes("difficulty")||h.includes("competition")||h.includes("competitor")||h.includes("position")||h.includes("opportunity")||h.includes("score")||h.includes("ranking")||h.includes("calculate")||h.includes("priority")||h.includes("initial data")||h.includes("remaining")||h.includes("business overview")||h.includes("key recommendations")||h.includes("top opportunities")||h.includes("structure")||h.includes("summary")||h.includes("analysis")||h.includes("the gap is")||h.includes("i have performed")||h.startsWith("the ")||h.startsWith("i ")||T.includes("---")||T.includes("===")||T.includes("***")||T.trim().length<3||/^[#\*\-\+\d\.\s]+$/.test(T.trim())||/^\d+\.\s*$/.test(T.trim())||/^[|:\-\s]+$/.test(T.trim()))return;const J=parseInt((w==null?void 0:w.toString().replace(/[^\d]/g,""))||"0")||0,de=parseInt((S==null?void 0:S.toString().replace(/[^\d]/g,""))||"0")||0,ar=parseFloat((le==null?void 0:le.toString().replace(/[^\d.]/g,""))||"0")||0,Le=parseInt((fe==null?void 0:fe.toString().replace(/[^\d]/g,""))||"0")||Math.floor(Math.random()*10)+1;let Be="missing";if(W){const Ue=W.toString().toLowerCase();Ue.includes("lower")||Ue.includes("rank")?Be="lower_rank":(Ue.includes("missing")||Ue==="missing")&&(Be="missing")}const xr={keyword:T.trim(),search_volume:J,difficulty:de,competition:de<30?"low":de<60?"medium":"high",cpc:Math.random()*8+2,competitor_position:Le,your_position:Be==="missing"?null:Le+Math.floor(Math.random()*20)+5,gap_type:Be,opportunity_score:ar>0?ar:J/(de+1)*(Be==="missing"?2:1)};xr.keyword&&xr.search_volume>0&&i.push(xr)}),i.length>0))break}if(i.length>0)return i;const m=a.split(`
`).filter(l=>l.includes("keyword")||l.includes("search volume")||l.match(/^\d+\.\s/)||l.match(/^[•\-\*]\s/)||l.includes("|"));if(m.length>0){const l=Sr(m);if(l.length>0)return l}const p=f(a);if(p.length>0)return p}catch(d){console.error("Error parsing AI response:",d)}return A(a)}function Sr(a){const s=[];for(const v of a){const d=[/(\d+)\.\s*([^-]+?)\s*-\s*volume:\s*(\d+),\s*difficulty:\s*(\d+)/i,/[•\-\*]\s*([^(]+?)\s*\(volume:\s*(\d+),\s*difficulty:\s*(\d+)\)/i,/([^|]+?)\s*\|\s*(\d+)\s*\|\s*(\d+)/,/([a-zA-Z][a-zA-Z\s]{10,50})/];for(const i of d){const m=v.match(i);if(m){let p,l,y;i===d[0]?[,,p,l,y]=m:i===d[1]?[,p,l,y]=m:i===d[2]?[,p,l,y]=m:([,p]=m,l=Math.floor(Math.random()*2e4)+1e3,y=Math.floor(Math.random()*60)+20);const I=parseInt((l==null?void 0:l.toString())||"0")||Math.floor(Math.random()*2e4)+1e3,$=parseInt((y==null?void 0:y.toString())||"0")||Math.floor(Math.random()*60)+20;if(p&&p.trim().length>3){s.push({keyword:p.trim(),search_volume:I,difficulty:$,competition:$<30?"low":$<60?"medium":"high",cpc:Math.random()*8+2,competitor_position:Math.floor(Math.random()*10)+1,your_position:null,gap_type:"missing",opportunity_score:I/($+1)*2});break}}}}return s.slice(0,15)}function f(a){var i;const s=[/"([^"]+)"/g,/keyword[:\s]+([a-zA-Z][a-zA-Z\s]{5,40})/gi,/\b([a-zA-Z][a-zA-Z\s]{8,35})\b/g],v=new Set;for(const m of s){const p=a.matchAll(m);for(const l of p){const y=(i=l[1])==null?void 0:i.trim();y&&y.length>5&&y.length<50&&!y.toLowerCase().includes("analysis")&&!y.toLowerCase().includes("report")&&!y.toLowerCase().includes("strategy")&&!y.toLowerCase().includes("competitor")&&y.split(" ").length>=2&&v.add(y)}}return Array.from(v).slice(0,10).map(m=>{const p=Math.floor(Math.random()*15e3)+1e3,l=Math.floor(Math.random()*50)+25;return{keyword:m,search_volume:p,difficulty:l,competition:l<35?"low":l<50?"medium":"high",cpc:Math.random()*6+2,competitor_position:Math.floor(Math.random()*8)+1,your_position:null,gap_type:"missing",opportunity_score:p/(l+1)*2}})}function A(a){const s=["agile project management","enterprise software solutions","cloud infrastructure management","devops automation tools","container orchestration solutions","continuous integration tools","software development lifecycle","api integration platform","machine learning operations","microservices architecture"],v=s.filter(i=>a.toLowerCase().includes(i.toLowerCase()));return(v.length>0?v:s.slice(0,8)).map((i,m)=>{const p=Math.random()>.6,l=Math.floor(Math.random()*20)+1,y=p?l+Math.floor(Math.random()*30)+5:null,I=Math.floor(Math.random()*25e3)+1e3,$=Math.floor(Math.random()*60)+20;return{keyword:i,search_volume:I,difficulty:$,competition:$<35?"low":$<55?"medium":"high",cpc:Math.random()*12+2,competitor_position:l,your_position:y,gap_type:y===null?"missing":"lower_rank",opportunity_score:I/($+1)*(y===null?2:1)}})}function E(){return["sales engagement platform","cold calling software","sales automation tools","lead generation platform","sales prospecting tools","crm integration software","sales cadence platform","outbound sales tools","sales dialer software","lead management system","sales pipeline software","contact management tools","sales analytics platform","sales reporting tools","sales team productivity"].map((s,v)=>{const d=Math.random()>.4,i=Math.floor(Math.random()*20)+1,m=d?i+Math.floor(Math.random()*30)+5:null,p=Math.floor(Math.random()*25e3)+1e3,l=Math.floor(Math.random()*60)+20;return{keyword:s,search_volume:p,difficulty:l,competition:l<35?"low":l<55?"medium":"high",cpc:Math.random()*12+2,competitor_position:i,your_position:m,gap_type:m===null?"missing":"lower_rank",opportunity_score:p/(l+1)*(m===null?2:1)}})}function re(a,s){let v="";switch(a){case"blog":v="Generate a blog content outline using the top 10 keywords from this analysis";break;case"competition":v="Analyze the competition level and ranking difficulty for each keyword group";break}c(D,v),Ve()}async function oe(a,s){const v=`Discover niche keywords for: ${a.join(", ")}. 
    Focus on long-tail variations with low competition. 
    ${s.industry?`Industry: ${s.industry}. `:""}
    Location: ${s.location}. 
    Volume range: ${s.volumeRange.min}-${s.volumeRange.max}. 
    Max difficulty: ${s.maxDifficulty}.
    Output format: table`;c(D,v),await Ve(),c(wr,[])}async function Z(a){c(ir,[]),c(ze,!1),c(Ge,""),c(xe,[{id:1,title:"Validating Domains",description:"Checking domain formats and accessibility...",status:"pending"},{id:2,title:"Analyzing Your Site",description:`Getting keywords for ${a.yourDomain}...`,status:"pending"},{id:3,title:"Analyzing Competitors",description:`Analyzing ${a.competitors.length} competitor${a.competitors.length>1?"s":""}...`,status:"pending"},{id:4,title:"Finding Intersections",description:"Comparing keyword rankings across domains...",status:"pending"},{id:5,title:"Calculating Gaps",description:"Identifying keyword opportunities...",status:"pending"},{id:6,title:"Generating Report",description:"Formatting your gap analysis results...",status:"pending"}]),c(Ne,0);const s=`Analyze keyword gaps between ${a.yourDomain} and competitors: ${a.competitors.join(", ")}. 
    Location: ${a.location}. 
    Minimum volume: ${a.minVolume}. 
    Maximum difficulty: ${a.maxDifficulty}.
    Gap type: ${a.gapType==="all"?"Show all gaps":a.gapType==="missing"?"Keywords competitors rank for but we don't":"Keywords where competitors outrank us"}.
    Output format: table`;c(Se,e(xe)),c(Pe,e(Ne)),c(D,s),Q().length,await Ve(),console.log("Gap analysis completed for:",a.yourDomain,"vs",a.competitors)}wt(()=>Q(),()=>{Q().length>0&&setTimeout(()=>{const a=document.querySelector(".messages-container");a&&(a.scrollTop=a.scrollHeight)},100)}),kt(),At();var U=Ao();Mt(a=>{$t.title="SEO Strategist - AI Agent"});var ie=t(U),Me=t(ie),Fe=t(Me),pe=t(Fe),tr=t(pe),lr=t(tr);Wr(lr,{class:"w-6 h-6 animate-pulse",style:"color: var(--primary-foreground);"}),r(tr),B(2),r(pe);var or=o(pe,2),q=t(or),De=t(q),me=o(De,2),br=t(me);gt(br,{class:"w-4 h-4 inline mr-1"}),B(),r(me);var he=o(me,2),_e=t(he);qr(_e,{class:"w-4 h-4 inline mr-1"}),B(),r(he),r(q);var dr=o(q,2),Mr=t(dr);qr(Mr,{class:"w-4 h-4",style:"color: var(--accent-foreground);"}),B(2),r(dr),r(or),r(Fe),r(Me),r(ie);var Cr=o(ie,2),yr=t(Cr),n=t(yr),u=o(n,2);bt(u,{class:"w-4 h-4"}),B(2),r(yr),r(Cr);var b=o(Cr,2),j=t(b);{var L=a=>{var s=So(),v=t(s),d=t(v),i=t(d),m=t(i);{var p=P=>{var k=mo(),ge=t(k),Ke=t(ge);Wr(Ke,{class:"w-8 h-8",style:"color: var(--muted-foreground);"}),r(ge);var Ye=o(ge,6);mr(Ye,5,()=>Xe,Kr,(sr,ae)=>{var we=po(),ke=t(we),Ee=t(ke),cr=t(Ee);Ct(cr,()=>e(ae).icon,(ur,Gr)=>{Gr(ur,{class:"w-4 h-4",style:"color: var(--primary-foreground);"})}),r(Ee);var Re=o(Ee,2),vr=t(Re,!0);r(Re),r(ke);var Oe=o(ke,2),F=t(Oe,!0);r(Oe);var Ce=o(Oe,2),We=t(Ce,!0);r(Ce),r(we),N(()=>{we.disabled=e(C),K(vr,(e(ae),V(()=>e(ae).title))),K(F,(e(ae),V(()=>e(ae).description))),K(We,(e(ae),V(()=>e(ae).prompt)))}),R("click",we,()=>{c(D,e(ae).prompt)}),g(sr,we)}),r(Ye),r(k),g(P,k)};z(m,P=>{Q(),V(()=>Q().length===0)&&P(p)})}var l=o(m,2);mr(l,1,Q,Kr,(P,k)=>{var ge=yo(),Ke=t(ge),Ye=t(Ke);{var sr=H=>{Rt(H,{class:"w-5 h-5",style:"color: var(--primary-foreground);"})},ae=H=>{vt(H,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"})};z(Ye,H=>{e(k),V(()=>e(k).role==="user")?H(sr):H(ae,!1)})}r(Ke);var we=o(Ke,2),ke=t(we),Ee=t(ke),cr=t(Ee,!0);r(Ee);var Re=o(Ee,2),vr=t(Re);Et(vr,{class:"w-3 h-3",style:"color: var(--muted-foreground);"});var Oe=o(vr,2),F=t(Oe,!0);r(Oe),r(Re);var Ce=o(Re,2);{var We=H=>{var be=fo(),nr=t(be);nt(nr,{class:"w-3 h-3"}),B(),r(be),R("click",be,()=>$r(e(k))),g(H,be)};z(Ce,H=>{e(k),V(()=>e(k).role==="assistant"&&e(k).isReport)&&H(We)})}r(ke);var ur=o(ke,2);{var Gr=H=>{var be=go(),nr=t(be),pr=t(nr,!0);r(nr),r(be),N(()=>K(pr,(e(k),V(()=>e(k).content)))),g(H,be)},Xr=H=>{var be=bo(),nr=Ze(be),pr=t(nr),et=t(pr);mt(et,()=>(e(k),V(()=>Er(e(k).content)))),r(pr),r(nr);var Zr=o(nr,2),jr=t(Zr),Ie=t(jr);it(Ie,{class:"w-3 h-3"}),B(),r(jr);var $e=o(jr,2),Lr=t($e);zt(Lr,{class:"w-3 h-3"}),B(),r($e);var Vr=o($e,2),hr=t(Vr);Tt(hr,{class:"w-3 h-3"}),B(),r(Vr),r(Zr),R("click",jr,()=>Rr(e(k).content)),R("click",$e,()=>Pr(e(k).content)),R("click",Vr,()=>re("blog",e(k).content)),g(H,be)};z(ur,H=>{e(k),V(()=>e(k).role==="user")?H(Gr):H(Xr,!1)})}r(we),r(ge),N(H=>{ve(ge,1,`flex gap-4 ${e(k),V(()=>e(k).role==="user"?"flex-row-reverse":"")??""}`),ye(Ke,`background: var(--${e(k),V(()=>e(k).role==="user"?"primary":"secondary")??""}); border-color: var(--border); box-shadow: var(--shadow-sm);`),K(cr,(e(k),V(()=>e(k).role==="user"?"You":"SEO Strategist"))),K(F,H)},[()=>(e(k),V(()=>e(k).timestamp.toLocaleTimeString()))],dt),g(P,ge)});var y=o(l,2);{var I=P=>{var k=wo(),ge=t(k),Ke=t(ge);vt(Ke,{class:"w-5 h-5",style:"color: var(--secondary-foreground);"}),r(ge);var Ye=o(ge,2),sr=o(t(Ye),2),ae=t(sr);mr(ae,5,()=>e(Se),Oe=>Oe.id,(Oe,F)=>{var Ce=_o(),We=t(Ce),ur=t(We);{var Gr=Ie=>{var $e=xo(),Lr=t($e);yt(Lr,{class:"w-5 h-5 animate-scale-in",style:"color: var(--primary);"}),r($e),Yr(3,$e,()=>ft,()=>({duration:200})),g(Ie,$e)},Xr=(Ie,$e)=>{{var Lr=hr=>{Jr(hr,{class:"w-5 h-5 animate-spin",style:"color: var(--primary);"})},Vr=hr=>{xt(hr,{class:"w-5 h-5 opacity-30",style:"color: var(--muted-foreground);"})};z(Ie,hr=>{e(F),V(()=>e(F).status==="active")?hr(Lr):hr(Vr,!1)},$e)}};z(ur,Ie=>{e(F),V(()=>e(F).status==="completed")?Ie(Gr):Ie(Xr,!1)})}r(We);var H=o(We,2),be=t(H),nr=t(be,!0);r(be);var pr=o(be,2),et=t(pr,!0);r(pr);var Zr=o(pr,2);{var jr=Ie=>{var $e=ho(),Lr=t($e);r($e),N(()=>ye(Lr,`background: var(--primary); width: ${e(F),V(()=>e(F).progress)??""}%`)),g(Ie,$e)};z(Zr,Ie=>{e(F),V(()=>e(F).status==="active"&&e(F).progress)&&Ie(jr)})}r(H),r(Ce),N(()=>{ye(be,`color: ${e(F),V(()=>e(F).status==="pending"?"var(--muted-foreground)":"var(--foreground)")??""};${e(F),V(()=>e(F).status==="pending"?"opacity: 0.5":"")??""}`),K(nr,(e(F),V(()=>e(F).title))),ye(pr,`color: var(--muted-foreground);${e(F),V(()=>e(F).status==="pending"?"opacity: 0.5":"")??""}`),K(et,(e(F),V(()=>e(F).description)))}),Yr(3,Ce,()=>tt,()=>({duration:300})),g(Oe,Ce)}),r(ae);var we=o(ae,2),ke=t(we),Ee=o(t(ke),2),cr=t(Ee);r(Ee),r(ke);var Re=o(ke,2),vr=t(Re);r(Re),r(we),r(sr),r(Ye),r(k),N(()=>{K(cr,`${e(Pe)??""}%`),ye(vr,`background: linear-gradient(to right, var(--primary), var(--accent)); width: ${e(Pe)??""}%`)}),g(P,k)};z(y,P=>{e(C)&&P(I)})}r(i),r(d);var $=o(d,2),T=t($),w=o(t(T),2),S=o(t(w),2),W=t(S),fe=o(W,2),le=o(fe,2);r(S),r(w),r(T);var h=o(T,2),J=t(h),de=t(J);lt(de,{class:"w-4 h-4"});var ar=o(de,2);r(J);var Le=o(J,2);{var Be=P=>{var k=ko(),ge=t(k),Ke=o(t(ge),2);N(()=>{e(je),St(()=>{e(C)})});var Ye=t(Ke);Ye.value=Ye.__value="";var sr=o(Ye);sr.value=sr.__value="B2B SaaS";var ae=o(sr);ae.value=ae.__value="E-commerce";var we=o(ae);we.value=we.__value="Local business";var ke=o(we);ke.value=ke.__value="Content creators";var Ee=o(ke);Ee.value=Ee.__value="Enterprise",r(Ke),r(ge);var cr=o(ge,2),Re=o(t(cr),2);fr(Re),r(cr);var vr=o(cr,2),Oe=o(t(vr),2),F=t(Oe),Ce=o(F,2),We=o(Ce,2);r(Oe),r(vr),r(k),N(()=>{Ke.disabled=e(C),Re.disabled=e(C),ve(F,1,`px-2 py-1 text-xs font-medium transition-colors ${e(Te)==="awareness"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),F.disabled=e(C),ve(Ce,1,`px-2 py-1 text-xs font-medium transition-colors border-l border-r ${e(Te)==="consideration"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),Ce.disabled=e(C),ve(We,1,`px-2 py-1 text-xs font-medium transition-colors ${e(Te)==="decision"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),We.disabled=e(C)}),st(Ke,()=>e(je),ur=>c(je,ur)),He(Re,()=>e(gr),ur=>c(gr,ur)),R("click",F,()=>c(Te,"awareness")),R("click",Ce,()=>c(Te,"consideration")),R("click",We,()=>c(Te,"decision")),g(P,k)};z(Le,P=>{e(ee)&&P(Be)})}r(h);var xr=o(h,2),Ue=t(xr),Ar=t(Ue);pt(Ar),r(Ue);var Dr=o(Ue,2),Hr=t(Dr);{var Qr=P=>{var k=$o();g(P,k)},Or=P=>{Wr(P,{class:"w-4 h-4 animate-pulse"})};z(Hr,P=>{e(C)?P(Qr):P(Or,!1)})}B(),r(Dr),r(xr),r($),r(v),r(s),N(P=>{ve($,1,`input-wrapper input-wrapper-seo p-6 ${e(C)?"opacity-50 pointer-events-none":""}`),ve(W,1,`px-3 py-1 text-sm font-medium transition-colors ${e(ue)==="summary"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),W.disabled=e(C),ve(fe,1,`px-3 py-1 text-sm font-medium transition-colors border-l border-r ${e(ue)==="table"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),fe.disabled=e(C),ve(le,1,`px-3 py-1 text-sm font-medium transition-colors ${e(ue)==="blog"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),le.disabled=e(C),J.disabled=e(C),ve(ar,1,`text-xs ${e(ee)?"rotate-180":""} transition-transform`),rt(Ar,"placeholder",e(Je)),Ar.disabled=e(C),Dr.disabled=P},[()=>(e(D),e(C),V(()=>!e(D).trim()||e(C)))],dt),R("click",W,()=>c(ue,"summary")),R("click",fe,()=>c(ue,"table")),R("click",le,()=>c(ue,"blog")),R("click",J,()=>c(ee,!e(ee))),He(Ar,()=>e(D),P=>c(D,P)),R("keydown",Ar,kr),R("click",Dr,Ve),g(a,s)},Y=(a,s)=>{{var v=i=>{var m=Mo(),p=t(m),l=t(p);Yt(l,{onAnalyze:oe,get isLoading(){return e(C)},get discoveredKeywords(){return e(wr)}}),r(p),r(m),g(i,m)},d=(i,m)=>{{var p=l=>{var y=Co(),I=t(y),$=t(I);uo($,{onAnalyze:Z,get isLoading(){return e(C)},get gapKeywords(){return e(ir)},get progressSteps(){return e(xe)},get currentProgress(){return e(Ne)},get isMockData(){return e(ze)},get aiResponse(){return e(Ge)}}),r(I),r(y),g(l,y)};z(i,l=>{e(te)==="gap"&&l(p)},m)}};z(a,i=>{e(te)==="niche"?i(v):i(d,!1)},s)}};z(j,a=>{e(te)==="chat"?a(L):a(Y,!1)})}r(b),r(U),N(()=>{ve(De,1,`px-4 py-2 text-sm font-medium transition-colors ${e(te)==="chat"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),ve(me,1,`px-4 py-2 text-sm font-medium transition-colors border-l ${e(te)==="niche"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),ve(he,1,`px-4 py-2 text-sm font-medium transition-colors border-l ${e(te)==="gap"?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground"}`),rt(n,"href",`/dashboard/${G(),V(()=>G().params.envSlug)??""}`)}),R("click",De,()=>c(te,"chat")),R("click",me,()=>c(te,"niche")),R("click",he,()=>c(te,"gap")),g(se,U),at(),ne()}export{ta as component};

import"../chunks/CWj6FrbW.js";import{p as $t,f as D,t as lt,a as r,g as gt,$ as ht,s as o,c as p,b as I,e as $,q as W,n as N,v as F,i as h,j as A,o as E,r as g}from"../chunks/wnqW1tdD.js";import{h as xt,s as mt}from"../chunks/CDPCzm7q.js";import{i as dt}from"../chunks/BjRbZGyQ.js";import{c as s}from"../chunks/ojdN50pv.js";import{a as bt}from"../chunks/Cet13GU7.js";import{s as G}from"../chunks/Cmdkv-7M.js";import{a as Pt,b as H,s as J}from"../chunks/D5ITLM2v.js";import{s as yt,z as Ct,C as K,F as L,a as M,b as Q}from"../chunks/CbBNkXRp.js";import"../chunks/Cvx8ZW61.js";import"../chunks/hI_ygoix.js";import"../chunks/Dz_YqotQ.js";import{I as R}from"../chunks/aFtO4Q5C.js";import"../chunks/DpzY6icx.js";import{p as wt}from"../chunks/D4lkK9WL.js";import{B as Ft}from"../chunks/BFIy0mTe.js";var Dt=D("<!> <!> <!>",1),St=D("<!> <!> <!>",1),kt=D("<!> <!> <!>",1),zt=D('<p class="text-destructive text-sm font-bold text-center mt-3"> </p>'),Bt=D('<div class="text-center content-center max-w-lg mx-auto min-h-[100vh] pb-12 flex items-center place-content-center"><div class="flex flex-col w-64 lg:w-80"><div><h1 class="text-2xl font-bold mb-6">Create Profile</h1> <form class="grid gap-4" method="POST" action="/account/api?/updateProfile"><!> <!> <!> <!> <div class="mt-4"><!></div></form> <div class="text-sm mt-14"> <br/> <a class="underline" href="/account/sign_out">Sign out</a></div></div></div></div>');function Qt(it,Y){$t(Y,!0);const[j,vt]=Pt(),i=()=>J(k,"$formData",j),U=()=>J(_t,"$errors",j),V=()=>J(ft,"$delayed",j);let{session:q}=Y.data;const S=yt(Y.data.form,{validators:Ct(wt)}),{enhance:O,form:k,delayed:ft,errors:_t}=S;var T=Bt();xt(t=>{ht.title="Create Profile"});var X=p(T),Z=p(X),z=o(p(Z),2),tt=p(z);s(tt,()=>Q,(t,l)=>{l(t,{get form(){return S},name:"full_name",children:(m,B)=>{var d=I(),u=$(d);s(u,()=>K,(a,v)=>{v(a,{children:W,$$slots:{default:(x,b)=>{const P=E(()=>b.attrs);var f=Dt(),_=$(f);s(_,()=>L,(e,n)=>{n(e,{children:(C,nt)=>{N();var w=F("Your Name");r(C,w)},$$slots:{default:!0}})});var c=o(_,2);R(c,G(()=>A(P),{get value(){return i().full_name},set value(e){H(k,h(i).full_name=e,h(i))}}));var y=o(c,2);s(y,()=>M,(e,n)=>{n(e,{})}),r(x,f)}}})}),r(m,d)},$$slots:{default:!0}})});var et=o(tt,2);s(et,()=>Q,(t,l)=>{l(t,{get form(){return S},name:"company_name",children:(m,B)=>{var d=I(),u=$(d);s(u,()=>K,(a,v)=>{v(a,{children:W,$$slots:{default:(x,b)=>{const P=E(()=>b.attrs);var f=St(),_=$(f);s(_,()=>L,(e,n)=>{n(e,{children:(C,nt)=>{N();var w=F("Company Name");r(C,w)},$$slots:{default:!0}})});var c=o(_,2);R(c,G(()=>A(P),{get value(){return i().company_name},set value(e){H(k,h(i).company_name=e,h(i))}}));var y=o(c,2);s(y,()=>M,(e,n)=>{n(e,{})}),r(x,f)}}})}),r(m,d)},$$slots:{default:!0}})});var rt=o(et,2);s(rt,()=>Q,(t,l)=>{l(t,{get form(){return S},name:"website",children:(m,B)=>{var d=I(),u=$(d);s(u,()=>K,(a,v)=>{v(a,{children:W,$$slots:{default:(x,b)=>{const P=E(()=>b.attrs);var f=kt(),_=$(f);s(_,()=>L,(e,n)=>{n(e,{children:(C,nt)=>{N();var w=F("Company Website");r(C,w)},$$slots:{default:!0}})});var c=o(_,2);R(c,G(()=>A(P),{get value(){return i().website},set value(e){H(k,h(i).website=e,h(i))}}));var y=o(c,2);s(y,()=>M,(e,n)=>{n(e,{})}),r(x,f)}}})}),r(m,d)},$$slots:{default:!0}})});var at=o(rt,2);{var ct=t=>{var l=zt(),m=p(l,!0);g(l),lt(()=>mt(m,U()._errors[0])),r(t,l)};dt(at,t=>{U()._errors&&t(ct)})}var ot=o(at,2),ut=p(ot);Ft(ut,{type:"submit",class:"mt-3",get disabled(){return V()},children:(t,l)=>{var m=I(),B=$(m);{var d=a=>{var v=F("...");r(a,v)},u=a=>{var v=F("Create Profile");r(a,v)};dt(B,a=>{V()?a(d):a(u,!1)})}r(t,m)},$$slots:{default:!0}}),g(ot),g(z),bt(z,t=>O==null?void 0:O(t));var st=o(z,2),pt=p(st);N(3),g(st),g(Z),g(X),g(T),lt(()=>{var t;return mt(pt,`You are logged in as ${((t=q==null?void 0:q.user)==null?void 0:t.email)??"an anonymous user"??""}. `)}),r(it,T),gt(),vt()}export{Qt as component};

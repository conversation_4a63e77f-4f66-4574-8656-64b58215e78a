import"../chunks/CWj6FrbW.js";import{p as ft,b as j,e as l,a as t,g as ct,$ as pt,f as v,n as M,v as u,s as d,c as N,q as _t,i as y,j as ut,o as vt,r as O,t as $t}from"../chunks/wnqW1tdD.js";import{h as gt,s as ht}from"../chunks/CDPCzm7q.js";import{i as q}from"../chunks/BjRbZGyQ.js";import{c as s}from"../chunks/ojdN50pv.js";import{a as xt}from"../chunks/Cet13GU7.js";import{r as bt}from"../chunks/rh_XW2Tv.js";import{b as yt}from"../chunks/CJ-FD9ng.js";import{s as Ct}from"../chunks/Cmdkv-7M.js";import{a as Pt,b as Q,s as C}from"../chunks/D5ITLM2v.js";import{s as Ft,z as Dt,b as kt,C as zt,F as At,a as Bt}from"../chunks/CbBNkXRp.js";import{a as It,C as jt}from"../chunks/Dk7oig4_.js";import{C as qt}from"../chunks/DjwdYs0r.js";import"../chunks/Cvx8ZW61.js";import{C as wt,a as Et}from"../chunks/DMt7uXAk.js";import"../chunks/hI_ygoix.js";import"../chunks/Dz_YqotQ.js";import{o as St}from"../chunks/D4lkK9WL.js";import{I as Vt}from"../chunks/aFtO4Q5C.js";import"../chunks/DpzY6icx.js";import{B as Gt}from"../chunks/BFIy0mTe.js";var Ht=v("<!> <!>",1),Jt=v("<!> <!>",1),Kt=v("<!> <!>",1),Lt=v('<p class="text-destructive text-sm font-bold mt-1"> </p>'),Mt=v('<form method="post" class="grid gap-4"><input name="email" hidden/> <!> <!> <!></form>'),Nt=v("<!> <!>",1);function _r(R,P){ft(P,!0);const[h,T]=Pt(),f=()=>C(F,"$formData",h),U=()=>C(Y,"$constraints",h),w=()=>C(X,"$errors",h),E=()=>C(W,"$delayed",h),S=Ft(P.data.form,{validators:Dt(St)}),{form:F,enhance:D,delayed:W,errors:X,constraints:Y}=S;var V=j();gt(G=>{pt.title="Confirmation"});var Z=l(V);s(Z,()=>jt,(G,tt)=>{tt(G,{class:"mt-6",children:(rt,Ot)=>{var H=Nt(),J=l(H);s(J,()=>wt,(k,z)=>{z(k,{children:(A,at)=>{var i=Ht(),c=l(i);s(c,()=>Et,(p,$)=>{$(p,{class:"text-2xl font-bold text-center",children:(g,r)=>{M();var a=u("Enter your verification code");t(g,a)},$$slots:{default:!0}})});var x=d(c,2);s(x,()=>qt,(p,$)=>{$(p,{children:(g,r)=>{var a=j(),n=l(a);{var b=e=>{var o=u(`A 6-digit confirmation code has been sent to your email. Please verify
        your email to complete your account setup.`);t(e,o)},_=e=>{var o=u(`A 6-digit confirmation code has been sent to your email to confirm your
        email change.`);t(e,o)};q(n,e=>{P.data.type==="signup"?e(b):e(_,!1)})}t(g,a)},$$slots:{default:!0}})}),t(A,i)},$$slots:{default:!0}})});var ot=d(J,2);s(ot,()=>It,(k,z)=>{z(k,{children:(A,at)=>{var i=Mt(),c=N(i);bt(c);var x=d(c,2);s(x,()=>kt,(r,a)=>{a(r,{get form(){return S},name:"code",children:(n,b)=>{var _=Kt(),e=l(_);s(e,()=>zt,(m,B)=>{B(m,{children:_t,$$slots:{default:(et,st)=>{const nt=vt(()=>st.attrs);var K=Jt(),L=l(K);s(L,()=>At,(I,mt)=>{mt(I,{children:(lt,Qt)=>{M();var dt=u("6-digit code");t(lt,dt)},$$slots:{default:!0}})});var it=d(L,2);Vt(it,Ct(()=>ut(nt),()=>U().code,{get value(){return f().code},set value(I){Q(F,y(f).code=I,y(f))}})),t(et,K)}}})});var o=d(e,2);s(o,()=>Bt,(m,B)=>{B(m,{})}),t(n,_)},$$slots:{default:!0}})});var p=d(x,2);{var $=r=>{var a=Lt(),n=N(a,!0);O(a),$t(()=>ht(n,w()._errors[0])),t(r,a)};q(p,r=>{w()._errors&&r($)})}var g=d(p,2);Gt(g,{type:"submit",get disabled(){return E()},class:"w-full",children:(r,a)=>{var n=j(),b=l(n);{var _=o=>{var m=u("...");t(o,m)},e=o=>{var m=u("Verify");t(o,m)};q(b,o=>{E()?o(_):o(e,!1)})}t(r,n)},$$slots:{default:!0}}),O(i),xt(i,r=>D==null?void 0:D(r)),yt(c,()=>f().email,r=>Q(F,y(f).email=r,y(f))),t(A,i)},$$slots:{default:!0}})}),t(rt,H)},$$slots:{default:!0}})}),t(R,V),ct(),T()}export{_r as component};

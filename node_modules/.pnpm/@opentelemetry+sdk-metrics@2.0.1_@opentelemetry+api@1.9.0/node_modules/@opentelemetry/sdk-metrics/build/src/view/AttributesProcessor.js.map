{"version": 3, "file": "AttributesProcessor.js", "sourceRoot": "", "sources": ["../../../src/view/AttributesProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAoBH,MAAM,uBAAuB;IAC3B,OAAO,CAAC,QAAoB,EAAE,QAAkB;QAC9C,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED,MAAM,wBAAwB;IACC;IAA7B,YAA6B,WAAmC;QAAnC,gBAAW,GAAX,WAAW,CAAwB;IAAG,CAAC;IACpE,OAAO,CAAC,QAAoB,EAAE,OAAiB;QAC7C,IAAI,kBAAkB,GAAG,QAAQ,CAAC;QAClC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,kBAAkB,GAAG,SAAS,CAAC,OAAO,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;SACrE;QACD,OAAO,kBAAkB,CAAC;IAC5B,CAAC;CACF;AAED,MAAM,kBAAkB;IACF;IAApB,YAAoB,sBAAgC;QAAhC,2BAAsB,GAAtB,sBAAsB,CAAU;IAAG,CAAC;IAExD,OAAO,CAAC,QAAoB,EAAE,QAAkB;QAC9C,MAAM,kBAAkB,GAAe,EAAE,CAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;aAClB,MAAM,CAAC,aAAa,CAAC,EAAE,CACtB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,aAAa,CAAC,CACpD;aACA,OAAO,CACN,aAAa,CAAC,EAAE,CACd,CAAC,kBAAkB,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAChE,CAAC;QACJ,OAAO,kBAAkB,CAAC;IAC5B,CAAC;CACF;AAED,MAAM,iBAAiB;IACD;IAApB,YAAoB,qBAA+B;QAA/B,0BAAqB,GAArB,qBAAqB,CAAU;IAAG,CAAC;IAEvD,OAAO,CAAC,QAAoB,EAAE,QAAkB;QAC9C,MAAM,kBAAkB,GAAe,EAAE,CAAC;QAC1C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;aAClB,MAAM,CACL,aAAa,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,aAAa,CAAC,CACrE;aACA,OAAO,CACN,aAAa,CAAC,EAAE,CACd,CAAC,kBAAkB,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAChE,CAAC;QACJ,OAAO,kBAAkB,CAAC;IAC5B,CAAC;CACF;AAED;;;;GAIG;AACH,SAAgB,6BAA6B;IAC3C,OAAO,IAAI,CAAC;AACd,CAAC;AAFD,sEAEC;AAED;;;;;;GAMG;AACH,SAAgB,8BAA8B,CAC5C,UAAkC;IAElC,OAAO,IAAI,wBAAwB,CAAC,UAAU,CAAC,CAAC;AAClD,CAAC;AAJD,wEAIC;AAED;;;GAGG;AACH,SAAgB,kCAAkC,CAChD,kBAA4B;IAE5B,OAAO,IAAI,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACpD,CAAC;AAJD,gFAIC;AAED;;GAEG;AACH,SAAgB,iCAAiC,CAC/C,iBAA2B;IAE3B,OAAO,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AAClD,CAAC;AAJD,8EAIC;AAED,MAAM,IAAI,GAAG,IAAI,uBAAuB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, Attributes } from '@opentelemetry/api';\n\n/**\n * The {@link AttributesProcessor} is responsible for customizing which\n * attribute(s) are to be reported as metrics dimension(s) and adding\n * additional dimension(s) from the {@link Context}.\n */\nexport interface IAttributesProcessor {\n  /**\n   * Process the metric instrument attributes.\n   *\n   * @param incoming The metric instrument attributes.\n   * @param context The active context when the instrument is synchronous.\n   * `undefined` otherwise.\n   */\n  process: (incoming: Attributes, context?: Context) => Attributes;\n}\n\nclass NoopAttributesProcessor implements IAttributesProcessor {\n  process(incoming: Attributes, _context?: Context) {\n    return incoming;\n  }\n}\n\nclass MultiAttributesProcessor implements IAttributesProcessor {\n  constructor(private readonly _processors: IAttributesProcessor[]) {}\n  process(incoming: Attributes, context?: Context): Attributes {\n    let filteredAttributes = incoming;\n    for (const processor of this._processors) {\n      filteredAttributes = processor.process(filteredAttributes, context);\n    }\n    return filteredAttributes;\n  }\n}\n\nclass AllowListProcessor implements IAttributesProcessor {\n  constructor(private _allowedAttributeNames: string[]) {}\n\n  process(incoming: Attributes, _context?: Context): Attributes {\n    const filteredAttributes: Attributes = {};\n    Object.keys(incoming)\n      .filter(attributeName =>\n        this._allowedAttributeNames.includes(attributeName)\n      )\n      .forEach(\n        attributeName =>\n          (filteredAttributes[attributeName] = incoming[attributeName])\n      );\n    return filteredAttributes;\n  }\n}\n\nclass DenyListProcessor implements IAttributesProcessor {\n  constructor(private _deniedAttributeNames: string[]) {}\n\n  process(incoming: Attributes, _context?: Context): Attributes {\n    const filteredAttributes: Attributes = {};\n    Object.keys(incoming)\n      .filter(\n        attributeName => !this._deniedAttributeNames.includes(attributeName)\n      )\n      .forEach(\n        attributeName =>\n          (filteredAttributes[attributeName] = incoming[attributeName])\n      );\n    return filteredAttributes;\n  }\n}\n\n/**\n * @internal\n *\n * Create an {@link IAttributesProcessor} that acts as a simple pass-through for attributes.\n */\nexport function createNoopAttributesProcessor(): IAttributesProcessor {\n  return NOOP;\n}\n\n/**\n * @internal\n *\n * Create an {@link IAttributesProcessor} that applies all processors from the provided list in order.\n *\n * @param processors Processors to apply in order.\n */\nexport function createMultiAttributesProcessor(\n  processors: IAttributesProcessor[]\n): IAttributesProcessor {\n  return new MultiAttributesProcessor(processors);\n}\n\n/**\n * Create an {@link IAttributesProcessor} that filters by allowed attribute names and drops any names that are not in the\n * allow list.\n */\nexport function createAllowListAttributesProcessor(\n  attributeAllowList: string[]\n): IAttributesProcessor {\n  return new AllowListProcessor(attributeAllowList);\n}\n\n/**\n * Create an {@link IAttributesProcessor} that drops attributes based on the names provided in the deny list\n */\nexport function createDenyListAttributesProcessor(\n  attributeDenyList: string[]\n): IAttributesProcessor {\n  return new DenyListProcessor(attributeDenyList);\n}\n\nconst NOOP = new NoopAttributesProcessor();\n"]}
{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/export/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricProducer } from '@opentelemetry/sdk-metrics';\n\n/**\n * Configuration interface for prometheus exporter\n */\nexport interface ExporterConfig {\n  /**\n   * App prefix for metrics, if needed\n   *\n   * @default ''\n   * */\n  prefix?: string;\n\n  /**\n   * Append timestamp to metrics\n   * @default true\n   */\n  appendTimestamp?: boolean;\n\n  /**\n   * Endpoint the metrics should be exposed at with preceding slash\n   * @default '/metrics'\n   */\n  endpoint?: string;\n\n  /**\n   * @default undefined (all interfaces)\n   */\n  host?: string;\n\n  /**\n   * Port number for Prometheus exporter server\n   *\n   * Default registered port is 9464:\n   * https://github.com/prometheus/prometheus/wiki/Default-port-allocations\n   * @default 9464\n   */\n  port?: number;\n\n  /**\n   * Prevent the Prometheus exporter server from starting\n   * @default false\n   */\n  preventServerStart?: boolean;\n\n  /**\n   * **Note, this option is experimental**. Additional MetricProducers to use as a source of\n   * aggregated metric data in addition to the SDK's metric data. The resource returned by\n   * these MetricProducers is ignored; the SDK's resource will be used instead.\n   * @experimental\n   */\n  metricProducers?: MetricProducer[];\n\n  /**\n   * Regex pattern for defining which resource attributes will be applied\n   * as constant labels to the metrics.\n   * e.g. 'telemetry_.+' for all attributes starting with 'telemetry'.\n   * @default undefined (no resource attributes are applied)\n   */\n  withResourceConstantLabels?: RegExp;\n}\n"]}
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.zhTW = exports.zhCN = exports.vi = exports.ur = exports.ua = exports.tr = exports.th = exports.ta = exports.sv = exports.sl = exports.ru = exports.pt = exports.pl = exports.ps = exports.ota = exports.no = exports.nl = exports.ms = exports.mk = exports.ko = exports.kh = exports.ja = exports.it = exports.id = exports.hu = exports.he = exports.frCA = exports.fr = exports.fi = exports.fa = exports.es = exports.en = exports.de = exports.cs = exports.ca = exports.be = exports.az = exports.ar = void 0;
var ar_js_1 = require("./ar.js");
Object.defineProperty(exports, "ar", { enumerable: true, get: function () { return __importDefault(ar_js_1).default; } });
var az_js_1 = require("./az.js");
Object.defineProperty(exports, "az", { enumerable: true, get: function () { return __importDefault(az_js_1).default; } });
var be_js_1 = require("./be.js");
Object.defineProperty(exports, "be", { enumerable: true, get: function () { return __importDefault(be_js_1).default; } });
var ca_js_1 = require("./ca.js");
Object.defineProperty(exports, "ca", { enumerable: true, get: function () { return __importDefault(ca_js_1).default; } });
var cs_js_1 = require("./cs.js");
Object.defineProperty(exports, "cs", { enumerable: true, get: function () { return __importDefault(cs_js_1).default; } });
var de_js_1 = require("./de.js");
Object.defineProperty(exports, "de", { enumerable: true, get: function () { return __importDefault(de_js_1).default; } });
var en_js_1 = require("./en.js");
Object.defineProperty(exports, "en", { enumerable: true, get: function () { return __importDefault(en_js_1).default; } });
var es_js_1 = require("./es.js");
Object.defineProperty(exports, "es", { enumerable: true, get: function () { return __importDefault(es_js_1).default; } });
var fa_js_1 = require("./fa.js");
Object.defineProperty(exports, "fa", { enumerable: true, get: function () { return __importDefault(fa_js_1).default; } });
var fi_js_1 = require("./fi.js");
Object.defineProperty(exports, "fi", { enumerable: true, get: function () { return __importDefault(fi_js_1).default; } });
var fr_js_1 = require("./fr.js");
Object.defineProperty(exports, "fr", { enumerable: true, get: function () { return __importDefault(fr_js_1).default; } });
var fr_CA_js_1 = require("./fr-CA.js");
Object.defineProperty(exports, "frCA", { enumerable: true, get: function () { return __importDefault(fr_CA_js_1).default; } });
var he_js_1 = require("./he.js");
Object.defineProperty(exports, "he", { enumerable: true, get: function () { return __importDefault(he_js_1).default; } });
var hu_js_1 = require("./hu.js");
Object.defineProperty(exports, "hu", { enumerable: true, get: function () { return __importDefault(hu_js_1).default; } });
var id_js_1 = require("./id.js");
Object.defineProperty(exports, "id", { enumerable: true, get: function () { return __importDefault(id_js_1).default; } });
var it_js_1 = require("./it.js");
Object.defineProperty(exports, "it", { enumerable: true, get: function () { return __importDefault(it_js_1).default; } });
var ja_js_1 = require("./ja.js");
Object.defineProperty(exports, "ja", { enumerable: true, get: function () { return __importDefault(ja_js_1).default; } });
var kh_js_1 = require("./kh.js");
Object.defineProperty(exports, "kh", { enumerable: true, get: function () { return __importDefault(kh_js_1).default; } });
var ko_js_1 = require("./ko.js");
Object.defineProperty(exports, "ko", { enumerable: true, get: function () { return __importDefault(ko_js_1).default; } });
var mk_js_1 = require("./mk.js");
Object.defineProperty(exports, "mk", { enumerable: true, get: function () { return __importDefault(mk_js_1).default; } });
var ms_js_1 = require("./ms.js");
Object.defineProperty(exports, "ms", { enumerable: true, get: function () { return __importDefault(ms_js_1).default; } });
var nl_js_1 = require("./nl.js");
Object.defineProperty(exports, "nl", { enumerable: true, get: function () { return __importDefault(nl_js_1).default; } });
var no_js_1 = require("./no.js");
Object.defineProperty(exports, "no", { enumerable: true, get: function () { return __importDefault(no_js_1).default; } });
var ota_js_1 = require("./ota.js");
Object.defineProperty(exports, "ota", { enumerable: true, get: function () { return __importDefault(ota_js_1).default; } });
var ps_js_1 = require("./ps.js");
Object.defineProperty(exports, "ps", { enumerable: true, get: function () { return __importDefault(ps_js_1).default; } });
var pl_js_1 = require("./pl.js");
Object.defineProperty(exports, "pl", { enumerable: true, get: function () { return __importDefault(pl_js_1).default; } });
var pt_js_1 = require("./pt.js");
Object.defineProperty(exports, "pt", { enumerable: true, get: function () { return __importDefault(pt_js_1).default; } });
var ru_js_1 = require("./ru.js");
Object.defineProperty(exports, "ru", { enumerable: true, get: function () { return __importDefault(ru_js_1).default; } });
var sl_js_1 = require("./sl.js");
Object.defineProperty(exports, "sl", { enumerable: true, get: function () { return __importDefault(sl_js_1).default; } });
var sv_js_1 = require("./sv.js");
Object.defineProperty(exports, "sv", { enumerable: true, get: function () { return __importDefault(sv_js_1).default; } });
var ta_js_1 = require("./ta.js");
Object.defineProperty(exports, "ta", { enumerable: true, get: function () { return __importDefault(ta_js_1).default; } });
var th_js_1 = require("./th.js");
Object.defineProperty(exports, "th", { enumerable: true, get: function () { return __importDefault(th_js_1).default; } });
var tr_js_1 = require("./tr.js");
Object.defineProperty(exports, "tr", { enumerable: true, get: function () { return __importDefault(tr_js_1).default; } });
var ua_js_1 = require("./ua.js");
Object.defineProperty(exports, "ua", { enumerable: true, get: function () { return __importDefault(ua_js_1).default; } });
var ur_js_1 = require("./ur.js");
Object.defineProperty(exports, "ur", { enumerable: true, get: function () { return __importDefault(ur_js_1).default; } });
var vi_js_1 = require("./vi.js");
Object.defineProperty(exports, "vi", { enumerable: true, get: function () { return __importDefault(vi_js_1).default; } });
var zh_CN_js_1 = require("./zh-CN.js");
Object.defineProperty(exports, "zhCN", { enumerable: true, get: function () { return __importDefault(zh_CN_js_1).default; } });
var zh_TW_js_1 = require("./zh-TW.js");
Object.defineProperty(exports, "zhTW", { enumerable: true, get: function () { return __importDefault(zh_TW_js_1).default; } });

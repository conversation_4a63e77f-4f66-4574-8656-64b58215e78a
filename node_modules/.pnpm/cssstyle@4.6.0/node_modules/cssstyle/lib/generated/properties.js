"use strict";
// autogenerated - 2025-06-25
// https://www.w3.org/Style/CSS/all-properties.en.html

var external_dependency_parsers_0 = require("../parsers.js");
var external_dependency_strings_1 = require("../utils/strings.js");
var backgroundImage_export_parse, backgroundImage_export_isValid, backgroundImage_export_definition;
backgroundImage_export_parse = function parse(v) {
  return external_dependency_parsers_0.parseImage(v);
};
backgroundImage_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v, ["none"]) === "string") {
    return true;
  }
  return typeof backgroundImage_export_parse(v) === "string";
};
backgroundImage_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("background", "");
      this._setProperty("background-image", v);
    } else {
      this._setProperty("background-image", backgroundImage_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("background-image");
  },
  enumerable: true,
  configurable: true
};
var backgroundPosition_export_parse, backgroundPosition_export_isValid, backgroundPosition_export_definition;
backgroundPosition_export_parse = function parse(v) {
  const parts = external_dependency_parsers_0.splitValue(v);
  if (!parts.length || parts.length > 2) {
    return;
  }
  const validKeywordsX = ["left", "center", "right"];
  const validKeywordsY = ["top", "center", "bottom"];
  if (parts.length === 1) {
    const dim = external_dependency_parsers_0.parseMeasurement(parts[0]);
    if (dim) {
      return dim;
    }
    const validKeywords = new Set([...validKeywordsX, ...validKeywordsY]);
    return external_dependency_parsers_0.parseKeyword(v, [...validKeywords]);
  }
  const [partX, partY] = parts;
  const posX = external_dependency_parsers_0.parseMeasurement(partX) || external_dependency_parsers_0.parseKeyword(partX, validKeywordsX);
  if (posX) {
    const posY = external_dependency_parsers_0.parseMeasurement(partY) || external_dependency_parsers_0.parseKeyword(partY, validKeywordsY);
    if (posY) {
      return `${posX} ${posY}`;
    }
  }
};
backgroundPosition_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof backgroundPosition_export_parse(v) === "string";
};
backgroundPosition_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("background", "");
      this._setProperty("background-position", v);
    } else {
      this._setProperty("background-position", backgroundPosition_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("background-position");
  },
  enumerable: true,
  configurable: true
};
var backgroundRepeat_export_parse, backgroundRepeat_export_isValid, backgroundRepeat_export_definition;
backgroundRepeat_export_parse = function parse(v) {
  const keywords = ["repeat", "repeat-x", "repeat-y", "no-repeat", "space", "round"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
backgroundRepeat_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof backgroundRepeat_export_parse(v) === "string";
};
backgroundRepeat_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("background", "");
      this._setProperty("background-repeat", v);
    } else {
      this._setProperty("background-repeat", backgroundRepeat_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("background-repeat");
  },
  enumerable: true,
  configurable: true
};
var backgroundAttachment_export_parse, backgroundAttachment_export_isValid, backgroundAttachment_export_definition;
backgroundAttachment_export_parse = function parse(v) {
  const keywords = ["fixed", "scroll", "local"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
backgroundAttachment_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof backgroundAttachment_export_parse(v) === "string";
};
backgroundAttachment_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("background", "");
      this._setProperty("background-attachment", v);
    } else {
      this._setProperty("background-attachment", backgroundAttachment_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("background-attachment");
  },
  enumerable: true,
  configurable: true
};
var backgroundColor_export_parse, backgroundColor_export_isValid, backgroundColor_export_definition;
backgroundColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
backgroundColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
backgroundColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("background", "");
      this._setProperty("background-color", v);
    } else {
      this._setProperty("background-color", backgroundColor_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("background-color");
  },
  enumerable: true,
  configurable: true
};
var background_export_definition;
// FIXME:
// * support multiple backgrounds
// * also fix longhands

const background_local_var_shorthandFor = new Map([["background-image", {
  parse: backgroundImage_export_parse,
  isValid: backgroundImage_export_isValid,
  definition: backgroundImage_export_definition
}], ["background-position", {
  parse: backgroundPosition_export_parse,
  isValid: backgroundPosition_export_isValid,
  definition: backgroundPosition_export_definition
}], ["background-repeat", {
  parse: backgroundRepeat_export_parse,
  isValid: backgroundRepeat_export_isValid,
  definition: backgroundRepeat_export_definition
}], ["background-attachment", {
  parse: backgroundAttachment_export_parse,
  isValid: backgroundAttachment_export_isValid,
  definition: backgroundAttachment_export_definition
}], ["background-color", {
  parse: backgroundColor_export_parse,
  isValid: backgroundColor_export_isValid,
  definition: backgroundColor_export_definition
}]]);
background_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (/^none$/i.test(v)) {
      for (const [key] of background_local_var_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty("background", external_dependency_strings_1.asciiLowercase(v));
    } else if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of background_local_var_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty("background", v);
    } else {
      this._shorthandSetter("background", v, background_local_var_shorthandFor);
    }
  },
  get() {
    let val = this.getPropertyValue("background");
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    val = this._shorthandGetter("background", background_local_var_shorthandFor);
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var borderWidth_export_parse, borderWidth_export_isValid, borderWidth_export_definition;
borderWidth_export_parse = function parse(v) {
  const keywords = ["thin", "medium", "thick"];
  const key = external_dependency_parsers_0.parseKeyword(v, keywords);
  if (key) {
    return key;
  }
  return external_dependency_parsers_0.parseLength(v, true);
};
borderWidth_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderWidth_export_parse(v) === "string";
};
borderWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-width", v);
    } else {
      const positions = ["top", "right", "bottom", "left"];
      this._implicitSetter("border", "width", v, borderWidth_export_isValid, borderWidth_export_parse, positions);
    }
  },
  get() {
    return this.getPropertyValue("border-width");
  },
  enumerable: true,
  configurable: true
};
var borderStyle_export_parse, borderStyle_export_isValid, borderStyle_export_definition;
borderStyle_export_parse = function parse(v) {
  const keywords = ["none", "hidden", "dotted", "dashed", "solid", "double", "groove", "ridge", "inset", "outset"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
borderStyle_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderStyle_export_parse(v) === "string";
};
borderStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (/^none$/i.test(v)) {
      v = "";
    }
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-style", v);
      return;
    }
    const positions = ["top", "right", "bottom", "left"];
    this._implicitSetter("border", "style", v, borderStyle_export_isValid, borderStyle_export_parse, positions);
  },
  get() {
    return this.getPropertyValue("border-style");
  },
  enumerable: true,
  configurable: true
};
var borderColor_export_parse, borderColor_export_isValid, borderColor_export_definition;
borderColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
borderColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
borderColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-color", v);
    } else {
      const positions = ["top", "right", "bottom", "left"];
      this._implicitSetter("border", "color", v, borderColor_export_isValid, borderColor_export_parse, positions);
    }
  },
  get() {
    return this.getPropertyValue("border-color");
  },
  enumerable: true,
  configurable: true
};
var border_export_definition;
const border_local_var_shorthandFor = new Map([["border-width", {
  parse: borderWidth_export_parse,
  isValid: borderWidth_export_isValid,
  definition: borderWidth_export_definition
}], ["border-style", {
  parse: borderStyle_export_parse,
  isValid: borderStyle_export_isValid,
  definition: borderStyle_export_definition
}], ["border-color", {
  parse: borderColor_export_parse,
  isValid: borderColor_export_isValid,
  definition: borderColor_export_definition
}]]);
border_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (/^none$/i.test(v)) {
      v = "";
    }
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of border_local_var_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty("border", v);
    } else {
      this._midShorthandSetter("border", v, border_local_var_shorthandFor, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    let val = this.getPropertyValue("border");
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    val = this._shorthandGetter("border", border_local_var_shorthandFor);
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var borderTopWidth_export_parse, borderTopWidth_export_isValid, borderTopWidth_export_definition;
borderTopWidth_export_parse = function parse(v) {
  const keywords = ["thin", "medium", "thick"];
  const key = external_dependency_parsers_0.parseKeyword(v, keywords);
  if (key) {
    return key;
  }
  return external_dependency_parsers_0.parseLength(v, true);
};
borderTopWidth_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderTopWidth_export_parse(v) === "string";
};
borderTopWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-top", "");
      this._setProperty("border-width", "");
    }
    this._setProperty("border-top-width", borderTopWidth_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-top-width");
  },
  enumerable: true,
  configurable: true
};
var borderTopStyle_export_parse, borderTopStyle_export_isValid, borderTopStyle_export_definition;
borderTopStyle_export_parse = function parse(v) {
  const keywords = ["none", "hidden", "dotted", "dashed", "solid", "double", "groove", "ridge", "inset", "outset"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
borderTopStyle_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderTopStyle_export_parse(v) === "string";
};
borderTopStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    const val = borderTopStyle_export_parse(v);
    if (val === "none" || val === "hidden" || v === "") {
      this._setProperty("border-top-style", "");
      this._setProperty("border-top-color", "");
      this._setProperty("border-top-width", "");
      return;
    }
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-top", "");
      this._setProperty("border-style", "");
    }
    this._setProperty("border-top-style", val);
  },
  get() {
    return this.getPropertyValue("border-top-style");
  },
  enumerable: true,
  configurable: true
};
var borderTopColor_export_parse, borderTopColor_export_isValid, borderTopColor_export_definition;
borderTopColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
borderTopColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
borderTopColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-top", "");
      this._setProperty("border-color", "");
    }
    this._setProperty("border-top-color", borderTopColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-top-color");
  },
  enumerable: true,
  configurable: true
};
var borderBottom_export_definition;
const borderBottom_local_var_shorthandFor = new Map([["border-bottom-width", {
  parse: borderTopWidth_export_parse,
  isValid: borderTopWidth_export_isValid,
  definition: borderTopWidth_export_definition
}], ["border-bottom-style", {
  parse: borderTopStyle_export_parse,
  isValid: borderTopStyle_export_isValid,
  definition: borderTopStyle_export_definition
}], ["border-bottom-color", {
  parse: borderTopColor_export_parse,
  isValid: borderTopColor_export_isValid,
  definition: borderTopColor_export_definition
}]]);
borderBottom_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of borderBottom_local_var_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty("border", "");
      this._setProperty("border-bottom", v);
    } else {
      this._shorthandSetter("border-bottom", v, borderBottom_local_var_shorthandFor);
    }
  },
  get() {
    let val = this.getPropertyValue("border-bottom");
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    val = this._shorthandGetter("border-bottom", borderBottom_local_var_shorthandFor);
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var borderBottomColor_export_parse, borderBottomColor_export_isValid, borderBottomColor_export_definition;
borderBottomColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
borderBottomColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
borderBottomColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-bottom", "");
      this._setProperty("border-color", "");
    }
    this._setProperty("border-bottom-color", borderBottomColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-bottom-color");
  },
  enumerable: true,
  configurable: true
};
var borderBottomStyle_export_parse, borderBottomStyle_export_isValid, borderBottomStyle_export_definition;
borderBottomStyle_export_parse = function parse(v) {
  const keywords = ["none", "hidden", "dotted", "dashed", "solid", "double", "groove", "ridge", "inset", "outset"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
borderBottomStyle_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderBottomStyle_export_parse(v) === "string";
};
borderBottomStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    const val = borderBottomStyle_export_parse(v);
    if (val === "none" || val === "hidden") {
      this._setProperty("border-bottom-style", "");
      this._setProperty("border-bottom-color", "");
      this._setProperty("border-bottom-width", "");
      return;
    }
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-bottom", "");
      this._setProperty("border-style", "");
    }
    this._setProperty("border-bottom-style", val);
  },
  get() {
    return this.getPropertyValue("border-bottom-style");
  },
  enumerable: true,
  configurable: true
};
var borderBottomWidth_export_parse, borderBottomWidth_export_isValid, borderBottomWidth_export_definition;
borderBottomWidth_export_parse = function parse(v) {
  const keywords = ["thin", "medium", "thick"];
  const key = external_dependency_parsers_0.parseKeyword(v, keywords);
  if (key) {
    return key;
  }
  return external_dependency_parsers_0.parseLength(v, true);
};
borderBottomWidth_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderBottomWidth_export_parse(v) === "string";
};
borderBottomWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-bottom", "");
      this._setProperty("border-width", "");
    }
    this._setProperty("border-bottom-width", borderBottomWidth_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-bottom-width");
  },
  enumerable: true,
  configurable: true
};
var borderCollapse_export_parse, borderCollapse_export_isValid, borderCollapse_export_definition;
borderCollapse_export_parse = function parse(v) {
  return external_dependency_parsers_0.parseKeyword(v, ["collapse", "separate"]);
};
borderCollapse_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderCollapse_export_parse(v) === "string";
};
borderCollapse_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("border-collapse", borderCollapse_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-collapse");
  },
  enumerable: true,
  configurable: true
};
var borderLeft_export_definition;
const borderLeft_local_var_shorthandFor = new Map([["border-left-width", {
  parse: borderTopWidth_export_parse,
  isValid: borderTopWidth_export_isValid,
  definition: borderTopWidth_export_definition
}], ["border-left-style", {
  parse: borderTopStyle_export_parse,
  isValid: borderTopStyle_export_isValid,
  definition: borderTopStyle_export_definition
}], ["border-left-color", {
  parse: borderTopColor_export_parse,
  isValid: borderTopColor_export_isValid,
  definition: borderTopColor_export_definition
}]]);
borderLeft_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of borderLeft_local_var_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty("border", "");
      this._setProperty("border-left", v);
    } else {
      this._shorthandSetter("border-left", v, borderLeft_local_var_shorthandFor);
    }
  },
  get() {
    let val = this.getPropertyValue("border-left");
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    val = this._shorthandGetter("border-left", borderLeft_local_var_shorthandFor);
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var borderLeftColor_export_parse, borderLeftColor_export_isValid, borderLeftColor_export_definition;
borderLeftColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
borderLeftColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
borderLeftColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-left", "");
      this._setProperty("border-color", "");
    }
    this._setProperty("border-left-color", borderLeftColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-left-color");
  },
  enumerable: true,
  configurable: true
};
var borderLeftStyle_export_parse, borderLeftStyle_export_isValid, borderLeftStyle_export_definition;
borderLeftStyle_export_parse = function parse(v) {
  const keywords = ["none", "hidden", "dotted", "dashed", "solid", "double", "groove", "ridge", "inset", "outset"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
borderLeftStyle_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderLeftStyle_export_parse(v) === "string";
};
borderLeftStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    const val = borderLeftStyle_export_parse(v);
    if (val === "none" || val === "hidden") {
      this._setProperty("border-left-style", "");
      this._setProperty("border-left-color", "");
      this._setProperty("border-left-width", "");
      return;
    }
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-left", "");
      this._setProperty("border-style", "");
    }
    this._setProperty("border-left-style", val);
  },
  get() {
    return this.getPropertyValue("border-left-style");
  },
  enumerable: true,
  configurable: true
};
var borderLeftWidth_export_parse, borderLeftWidth_export_isValid, borderLeftWidth_export_definition;
borderLeftWidth_export_parse = function parse(v) {
  const keywords = ["thin", "medium", "thick"];
  const key = external_dependency_parsers_0.parseKeyword(v, keywords);
  if (key) {
    return key;
  }
  return external_dependency_parsers_0.parseLength(v, true);
};
borderLeftWidth_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderLeftWidth_export_parse(v) === "string";
};
borderLeftWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-left", "");
      this._setProperty("border-width", "");
    }
    this._setProperty("border-left-width", borderLeftWidth_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-left-width");
  },
  enumerable: true,
  configurable: true
};
var borderRight_export_definition;
const borderRight_local_var_shorthandFor = new Map([["border-right-width", {
  parse: borderTopWidth_export_parse,
  isValid: borderTopWidth_export_isValid,
  definition: borderTopWidth_export_definition
}], ["border-right-style", {
  parse: borderTopStyle_export_parse,
  isValid: borderTopStyle_export_isValid,
  definition: borderTopStyle_export_definition
}], ["border-right-color", {
  parse: borderTopColor_export_parse,
  isValid: borderTopColor_export_isValid,
  definition: borderTopColor_export_definition
}]]);
borderRight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of borderRight_local_var_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty("border", "");
      this._setProperty("border-right", v);
    } else {
      this._shorthandSetter("border-right", v, borderRight_local_var_shorthandFor);
    }
  },
  get() {
    let val = this.getPropertyValue("border-right");
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    val = this._shorthandGetter("border-right", borderRight_local_var_shorthandFor);
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var borderRightColor_export_parse, borderRightColor_export_isValid, borderRightColor_export_definition;
borderRightColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
borderRightColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
borderRightColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-right", "");
      this._setProperty("border-color", "");
    }
    this._setProperty("border-right-color", borderRightColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-right-color");
  },
  enumerable: true,
  configurable: true
};
var borderRightStyle_export_parse, borderRightStyle_export_isValid, borderRightStyle_export_definition;
borderRightStyle_export_parse = function parse(v) {
  const keywords = ["none", "hidden", "dotted", "dashed", "solid", "double", "groove", "ridge", "inset", "outset"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
borderRightStyle_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderRightStyle_export_parse(v) === "string";
};
borderRightStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    const val = borderRightStyle_export_parse(v);
    if (val === "none" || val === "hidden") {
      this._setProperty("border-right-style", "");
      this._setProperty("border-right-color", "");
      this._setProperty("border-right-width", "");
      return;
    }
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-right", "");
      this._setProperty("border-style", "");
    }
    this._setProperty("border-right-style", val);
  },
  get() {
    return this.getPropertyValue("border-right-style");
  },
  enumerable: true,
  configurable: true
};
var borderRightWidth_export_parse, borderRightWidth_export_isValid, borderRightWidth_export_definition;
borderRightWidth_export_parse = function parse(v) {
  const keywords = ["thin", "medium", "thick"];
  const key = external_dependency_parsers_0.parseKeyword(v, keywords);
  if (key) {
    return key;
  }
  return external_dependency_parsers_0.parseLength(v, true);
};
borderRightWidth_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderRightWidth_export_parse(v) === "string";
};
borderRightWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("border", "");
      this._setProperty("border-right", "");
      this._setProperty("border-width", "");
    }
    this._setProperty("border-right-width", borderRightWidth_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-right-width");
  },
  enumerable: true,
  configurable: true
};
var borderSpacing_export_parse, borderSpacing_export_isValid, borderSpacing_export_definition;
borderSpacing_export_parse = function parse(v) {
  if (v === "") {
    return v;
  }
  const key = external_dependency_parsers_0.parseKeyword(v);
  if (key) {
    return key;
  }
  const parts = external_dependency_parsers_0.splitValue(v);
  if (!parts.length || parts.length > 2) {
    return;
  }
  const val = [];
  for (const part of parts) {
    const dim = external_dependency_parsers_0.parseLength(part);
    if (!dim) {
      return;
    }
    val.push(dim);
  }
  return val.join(" ");
};
borderSpacing_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof borderSpacing_export_parse(v) === "string";
};
borderSpacing_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("border-spacing", borderSpacing_export_parse(v));
  },
  get() {
    return this.getPropertyValue("border-spacing");
  },
  enumerable: true,
  configurable: true
};
var borderTop_export_definition;
const borderTop_local_var_shorthandFor = new Map([["border-top-width", {
  parse: borderTopWidth_export_parse,
  isValid: borderTopWidth_export_isValid,
  definition: borderTopWidth_export_definition
}], ["border-top-style", {
  parse: borderTopStyle_export_parse,
  isValid: borderTopStyle_export_isValid,
  definition: borderTopStyle_export_definition
}], ["border-top-color", {
  parse: borderTopColor_export_parse,
  isValid: borderTopColor_export_isValid,
  definition: borderTopColor_export_definition
}]]);
borderTop_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of borderTop_local_var_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty("border", "");
      this._setProperty("border-top", v);
    } else {
      this._shorthandSetter("border-top", v, borderTop_local_var_shorthandFor);
    }
  },
  get() {
    let val = this.getPropertyValue("border-top");
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    val = this._shorthandGetter("border-top", borderTop_local_var_shorthandFor);
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var bottom_export_parse, bottom_export_isValid, bottom_export_definition;
bottom_export_parse = function parse(v) {
  const dim = external_dependency_parsers_0.parseMeasurement(v);
  if (dim) {
    return dim;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
bottom_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof bottom_export_parse(v) === "string";
};
bottom_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("bottom", bottom_export_parse(v));
  },
  get() {
    return this.getPropertyValue("bottom");
  },
  enumerable: true,
  configurable: true
};
var clear_export_parse, clear_export_isValid, clear_export_definition;
clear_export_parse = function parse(v) {
  const keywords = ["inline-start", "inline-end", "block-start", "block-end", "left", "right", "top", "bottom", "both-inline", "both-block", "both", "none"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
clear_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof clear_export_parse(v) === "string";
};
clear_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("clear", clear_export_parse(v));
  },
  get() {
    return this.getPropertyValue("clear");
  },
  enumerable: true,
  configurable: true
};
var clip_export_parse, clip_export_isValid, clip_export_definition;
// deprecated
// @see https://drafts.fxtf.org/css-masking/#clip-property

clip_export_parse = function parse(v) {
  if (v === "") {
    return v;
  }
  const val = external_dependency_parsers_0.parseKeyword(v, ["auto"]);
  if (val) {
    return val;
  }
  // parse legacy <shape>
  v = external_dependency_strings_1.asciiLowercase(v);
  const matches = v.match(/^rect\(\s*(.*)\s*\)$/);
  if (!matches) {
    return;
  }
  const parts = matches[1].split(/\s*,\s*/);
  if (parts.length !== 4) {
    return;
  }
  const valid = parts.every(function (part, index) {
    const measurement = external_dependency_parsers_0.parseMeasurement(part.trim());
    parts[index] = measurement;
    return typeof measurement === "string";
  });
  if (!valid) {
    return;
  }
  return `rect(${parts.join(", ")})`;
};
clip_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof clip_export_parse(v) === "string";
};
clip_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("clip", clip_export_parse(v));
  },
  get() {
    return this.getPropertyValue("clip");
  },
  enumerable: true,
  configurable: true
};
var color_export_parse, color_export_isValid, color_export_definition;
color_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
color_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
color_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("color", color_export_parse(v));
  },
  get() {
    return this.getPropertyValue("color");
  },
  enumerable: true,
  configurable: true
};
var flexGrow_export_parse, flexGrow_export_isValid, flexGrow_export_definition;
flexGrow_export_parse = function parse(v) {
  return external_dependency_parsers_0.parseNumber(v, true);
};
flexGrow_export_isValid = function isValid(v) {
  return typeof flexGrow_export_parse(v) === "string";
};
flexGrow_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("flex", "");
      this._setProperty("flex-grow", v);
    } else {
      this._setProperty("flex-grow", flexGrow_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("flex-grow");
  },
  enumerable: true,
  configurable: true
};
var flexShrink_export_parse, flexShrink_export_isValid, flexShrink_export_definition;
flexShrink_export_parse = function parse(v) {
  return external_dependency_parsers_0.parseNumber(v, true);
};
flexShrink_export_isValid = function isValid(v) {
  return typeof flexShrink_export_parse(v) === "string";
};
flexShrink_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("flex", "");
      this._setProperty("flex-shrink", v);
    } else {
      this._setProperty("flex-shrink", flexShrink_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("flex-shrink");
  },
  enumerable: true,
  configurable: true
};
var flexBasis_export_parse, flexBasis_export_isValid, flexBasis_export_definition;
flexBasis_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v);
  if (val) {
    return val;
  }
  const keywords = ["content", "auto", "min-content", "max-content"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
flexBasis_export_isValid = function isValid(v) {
  return typeof flexBasis_export_parse(v) === "string";
};
flexBasis_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("flex", "");
      this._setProperty("flex-basis", v);
    } else {
      this._setProperty("flex-basis", flexBasis_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("flex-basis");
  },
  enumerable: true,
  configurable: true
};
var flex_export_parse, flex_export_isValid, flex_export_definition;
const flex_local_var_shorthandFor = new Map([["flex-grow", {
  parse: flexGrow_export_parse,
  isValid: flexGrow_export_isValid,
  definition: flexGrow_export_definition
}], ["flex-shrink", {
  parse: flexShrink_export_parse,
  isValid: flexShrink_export_isValid,
  definition: flexShrink_export_definition
}], ["flex-basis", {
  parse: flexBasis_export_parse,
  isValid: flexBasis_export_isValid,
  definition: flexBasis_export_definition
}]]);
flex_export_parse = function parse(v) {
  const key = external_dependency_parsers_0.parseKeyword(v, ["auto", "none"]);
  if (key) {
    if (key === "auto") {
      return "1 1 auto";
    }
    if (key === "none") {
      return "0 0 auto";
    }
    if (key === "initial") {
      return "0 1 auto";
    }
    return;
  }
  const obj = external_dependency_parsers_0.parseShorthand(v, flex_local_var_shorthandFor);
  if (obj) {
    const flex = {
      "flex-grow": "1",
      "flex-shrink": "1",
      "flex-basis": "0%"
    };
    const items = Object.entries(obj);
    for (const [property, value] of items) {
      flex[property] = value;
    }
    return [...Object.values(flex)].join(" ");
  }
};
flex_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof flex_export_parse(v) === "string";
};
flex_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._shorthandSetter("flex", "", flex_local_var_shorthandFor);
      this._setProperty("flex", v);
    } else {
      this._shorthandSetter("flex", flex_export_parse(v), flex_local_var_shorthandFor);
    }
  },
  get() {
    let val = this.getPropertyValue("flex");
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    val = this._shorthandGetter("flex", flex_local_var_shorthandFor);
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var float_export_parse, float_export_isValid, float_export_definition;
float_export_parse = function parse(v) {
  const keywords = ["left", "right", "none", "inline-start", "inline-end"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
float_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof float_export_parse(v) === "string";
};
float_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("float", float_export_parse(v));
  },
  get() {
    return this.getPropertyValue("float");
  },
  enumerable: true,
  configurable: true
};
var floodColor_export_parse, floodColor_export_isValid, floodColor_export_definition;
floodColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
floodColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
floodColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("flood-color", floodColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("flood-color");
  },
  enumerable: true,
  configurable: true
};
var fontStyle_export_parse, fontStyle_export_isValid, fontStyle_export_definition;
fontStyle_export_parse = function parse(v) {
  const keywords = ["normal", "italic", "oblique"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
fontStyle_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof fontStyle_export_parse(v) === "string";
};
fontStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("font", "");
      this._setProperty("font-style", v);
    } else {
      this._setProperty("font-style", fontStyle_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("font-style");
  },
  enumerable: true,
  configurable: true
};
var fontVariant_export_parse, fontVariant_export_isValid, fontVariant_export_definition;
fontVariant_export_parse = function parse(v) {
  const num = external_dependency_parsers_0.parseNumber(v, true);
  if (num && parseFloat(num) <= 1000) {
    return num;
  }
  const keywords = ["normal", "none", "small-caps"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
fontVariant_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof fontVariant_export_parse(v) === "string";
};
fontVariant_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("font", "");
      this._setProperty("font-valiant", v);
    } else {
      this._setProperty("font-variant", fontVariant_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("font-variant");
  },
  enumerable: true,
  configurable: true
};
var fontWeight_export_parse, fontWeight_export_isValid, fontWeight_export_definition;
fontWeight_export_parse = function parse(v) {
  const num = external_dependency_parsers_0.parseNumber(v, true);
  if (num && parseFloat(num) <= 1000) {
    return num;
  }
  const keywords = ["normal", "bold", "lighter", "bolder"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
fontWeight_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof fontWeight_export_parse(v) === "string";
};
fontWeight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("font", "");
      this._setProperty("font-weight", v);
    } else {
      this._setProperty("font-weight", fontWeight_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("font-weight");
  },
  enumerable: true,
  configurable: true
};
var fontSize_export_parse, fontSize_export_isValid, fontSize_export_definition;
fontSize_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v, true);
  if (val) {
    return val;
  }
  const keywords = ["xx-small", "x-small", "small", "medium", "large", "x-large", "xx-large", "xxx-large", "smaller", "larger"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
fontSize_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof fontSize_export_parse(v) === "string";
};
fontSize_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("font", "");
      this._setProperty("font-size", v);
    } else {
      this._setProperty("font-size", fontSize_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("font-size");
  },
  enumerable: true,
  configurable: true
};
var lineHeight_export_parse, lineHeight_export_isValid, lineHeight_export_definition;
lineHeight_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseKeyword(v, ["normal"]);
  if (val) {
    return val;
  }
  const num = external_dependency_parsers_0.parseNumber(v, true);
  if (num) {
    return num;
  }
  return external_dependency_parsers_0.parseMeasurement(v, true);
};
lineHeight_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof lineHeight_export_parse(v) === "string";
};
lineHeight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("font", "");
      this._setProperty("line-height", v);
    } else {
      this._setProperty("line-height", lineHeight_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("line-height");
  },
  enumerable: true,
  configurable: true
};
var fontFamily_export_parse, fontFamily_export_isValid, fontFamily_export_definition;
fontFamily_export_parse = function parse(v) {
  if (v === "") {
    return v;
  }
  const keywords = ["serif", "sans-serif", "cursive", "fantasy", "monospace", "system-ui", "math", "ui-serif", "ui-sans-serif", "ui-monospace", "ui-rounded"];
  const genericValues = ["fangsong", "kai", "khmer-mul", "nastaliq"];
  const val = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const font = [];
  let valid = false;
  for (const i of val) {
    const str = external_dependency_parsers_0.parseString(i);
    if (str) {
      font.push(str);
      valid = true;
      continue;
    }
    const key = external_dependency_parsers_0.parseKeyword(i, keywords);
    if (key) {
      font.push(key);
      valid = true;
      continue;
    }
    const obj = external_dependency_parsers_0.parseFunction(i);
    if (obj) {
      const {
        name,
        value
      } = obj;
      if (name === "generic" && genericValues.includes(value)) {
        font.push(`${name}(${value})`);
        valid = true;
        continue;
      }
    }
    // This implementation does not strictly follow the specification.
    // The spec does not require the first letter of the font-family to be
    // capitalized, and unquoted font-family names are not restricted to ASCII.
    // However, in the real world, the first letter of the ASCII font-family
    // names are capitalized, and unquoted font-family names do not contain
    // spaces, e.g. `Times`. And non-ASCII font-family names are quoted even
    // without spaces, e.g. `"メイリオ"`.
    // @see https://drafts.csswg.org/css-fonts/#font-family-prop
    if (i !== "undefined" && /^(?:[A-Z][A-Za-z\d-]+(?:\s+[A-Z][A-Za-z\d-]+)*|-?[a-z][a-z-]+)$/.test(i)) {
      font.push(i.trim());
      valid = true;
      continue;
    }
    if (!valid) {
      return;
    }
  }
  return font.join(", ");
};
fontFamily_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof fontFamily_export_parse(v) === "string";
};
fontFamily_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("font", "");
      this._setProperty("font-family", v);
    } else {
      this._setProperty("font-family", fontFamily_export_parse(v));
    }
  },
  get() {
    return this.getPropertyValue("font-family");
  },
  enumerable: true,
  configurable: true
};
var font_export_parse, font_export_definition;
const font_local_var_shorthandFor = new Map([["font-style", {
  parse: fontStyle_export_parse,
  isValid: fontStyle_export_isValid,
  definition: fontStyle_export_definition
}], ["font-variant", {
  parse: fontVariant_export_parse,
  isValid: fontVariant_export_isValid,
  definition: fontVariant_export_definition
}], ["font-weight", {
  parse: fontWeight_export_parse,
  isValid: fontWeight_export_isValid,
  definition: fontWeight_export_definition
}], ["font-size", {
  parse: fontSize_export_parse,
  isValid: fontSize_export_isValid,
  definition: fontSize_export_definition
}], ["line-height", {
  parse: lineHeight_export_parse,
  isValid: lineHeight_export_isValid,
  definition: lineHeight_export_definition
}], ["font-family", {
  parse: fontFamily_export_parse,
  isValid: fontFamily_export_isValid,
  definition: fontFamily_export_definition
}]]);
font_export_parse = function parse(v) {
  const keywords = ["caption", "icon", "menu", "message-box", "small-caption", "status-bar"];
  const key = external_dependency_parsers_0.parseKeyword(v, keywords);
  if (key) {
    return key;
  }
  const [fontBlock, ...families] = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const [fontBlockA, fontBlockB] = external_dependency_parsers_0.splitValue(fontBlock, {
    delimiter: "/"
  });
  const font = {
    "font-style": "normal",
    "font-variant": "normal",
    "font-weight": "normal"
  };
  const fontFamilies = new Set();
  if (fontBlockB) {
    const [lineB, ...familiesB] = fontBlockB.trim().split(" ");
    if (!lineB || !{
      parse: lineHeight_export_parse,
      isValid: lineHeight_export_isValid,
      definition: lineHeight_export_definition
    }.isValid(lineB) || !familiesB.length) {
      return;
    }
    const lineHeightB = {
      parse: lineHeight_export_parse,
      isValid: lineHeight_export_isValid,
      definition: lineHeight_export_definition
    }.parse(lineB);
    const familyB = familiesB.join(" ");
    if ({
      parse: fontFamily_export_parse,
      isValid: fontFamily_export_isValid,
      definition: fontFamily_export_definition
    }.isValid(familyB)) {
      fontFamilies.add({
        parse: fontFamily_export_parse,
        isValid: fontFamily_export_isValid,
        definition: fontFamily_export_definition
      }.parse(familyB));
    } else {
      return;
    }
    const parts = external_dependency_parsers_0.splitValue(fontBlockA.trim());
    const properties = ["font-style", "font-variant", "font-weight", "font-size"];
    for (const part of parts) {
      if (part === "normal") {
        continue;
      } else {
        for (const property of properties) {
          switch (property) {
            case "font-style":
            case "font-variant":
            case "font-weight":
            case "font-size":
              {
                const value = font_local_var_shorthandFor.get(property);
                if (value.isValid(part)) {
                  font[property] = value.parse(part);
                }
                break;
              }
            default:
          }
        }
      }
    }
    if (Object.hasOwn(font, "font-size")) {
      font["line-height"] = lineHeightB;
    } else {
      return;
    }
  } else {
    // FIXME: Switch to toReversed() when we can drop Node.js 18 support.
    const revParts = [...external_dependency_parsers_0.splitValue(fontBlockA.trim())].reverse();
    const revFontFamily = [];
    const properties = ["font-style", "font-variant", "font-weight", "line-height"];
    font["font-style"] = "normal";
    font["font-variant"] = "normal";
    font["font-weight"] = "normal";
    font["line-height"] = "normal";
    let fontSizeA;
    for (const part of revParts) {
      if (fontSizeA) {
        if (part === "normal") {
          continue;
        } else {
          for (const property of properties) {
            switch (property) {
              case "font-style":
              case "font-variant":
              case "font-weight":
              case "line-height":
                {
                  const value = font_local_var_shorthandFor.get(property);
                  if (value.isValid(part)) {
                    font[property] = value.parse(part);
                  }
                  break;
                }
              default:
            }
          }
        }
      } else if ({
        parse: fontSize_export_parse,
        isValid: fontSize_export_isValid,
        definition: fontSize_export_definition
      }.isValid(part)) {
        fontSizeA = {
          parse: fontSize_export_parse,
          isValid: fontSize_export_isValid,
          definition: fontSize_export_definition
        }.parse(part);
      } else if ({
        parse: fontFamily_export_parse,
        isValid: fontFamily_export_isValid,
        definition: fontFamily_export_definition
      }.isValid(part)) {
        revFontFamily.push(part);
      } else {
        return;
      }
    }
    const family = revFontFamily.reverse().join(" ");
    if (fontSizeA && {
      parse: fontFamily_export_parse,
      isValid: fontFamily_export_isValid,
      definition: fontFamily_export_definition
    }.isValid(family)) {
      font["font-size"] = fontSizeA;
      fontFamilies.add({
        parse: fontFamily_export_parse,
        isValid: fontFamily_export_isValid,
        definition: fontFamily_export_definition
      }.parse(family));
    } else {
      return;
    }
  }
  for (const family of families) {
    if ({
      parse: fontFamily_export_parse,
      isValid: fontFamily_export_isValid,
      definition: fontFamily_export_definition
    }.isValid(family)) {
      fontFamilies.add({
        parse: fontFamily_export_parse,
        isValid: fontFamily_export_isValid,
        definition: fontFamily_export_definition
      }.parse(family));
    } else {
      return;
    }
  }
  font["font-family"] = [...fontFamilies].join(", ");
  return font;
};
font_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (v === "" || external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of font_local_var_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty("font", v);
    } else {
      const obj = font_export_parse(v);
      if (!obj) {
        return;
      }
      const str = new Set();
      for (const [key] of font_local_var_shorthandFor) {
        const val = obj[key];
        if (typeof val === "string") {
          this._setProperty(key, val);
          if (val && val !== "normal" && !str.has(val)) {
            if (key === "line-height") {
              str.add(`/ ${val}`);
            } else {
              str.add(val);
            }
          }
        }
      }
      this._setProperty("font", [...str].join(" "));
    }
  },
  get() {
    const val = this.getPropertyValue("font");
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    const str = new Set();
    for (const [key] of font_local_var_shorthandFor) {
      const v = this.getPropertyValue(key);
      if (external_dependency_parsers_0.hasVarFunc(v)) {
        return "";
      }
      if (v && v !== "normal" && !str.has(v)) {
        if (key === "line-height") {
          str.add(`/ ${v}`);
        } else {
          str.add(`${v}`);
        }
      }
    }
    return [...str].join(" ");
  },
  enumerable: true,
  configurable: true
};
var height_export_parse, height_export_isValid, height_export_definition;
height_export_parse = function parse(v) {
  const dim = external_dependency_parsers_0.parseMeasurement(v, true);
  if (dim) {
    return dim;
  }
  const keywords = ["auto", "min-content", "max-content", "fit-content"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
height_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof height_export_parse(v) === "string";
};
height_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("height", height_export_parse(v));
  },
  get() {
    return this.getPropertyValue("height");
  },
  enumerable: true,
  configurable: true
};
var left_export_parse, left_export_isValid, left_export_definition;
left_export_parse = function parse(v) {
  const dim = external_dependency_parsers_0.parseMeasurement(v);
  if (dim) {
    return dim;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
left_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof left_export_parse(v) === "string";
};
left_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("left", left_export_parse(v));
  },
  get() {
    return this.getPropertyValue("left");
  },
  enumerable: true,
  configurable: true
};
var lightingColor_export_parse, lightingColor_export_isValid, lightingColor_export_definition;
lightingColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
lightingColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
lightingColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("lighting-color", lightingColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("lighting-color");
  },
  enumerable: true,
  configurable: true
};
var margin_export_parse, margin_export_isValid, margin_export_definition;
const margin_local_var_positions = ["top", "right", "bottom", "left"];
margin_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
margin_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof margin_export_parse(v) === "string";
};
margin_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._implicitSetter("margin", "", "", margin_export_isValid, margin_export_parse, margin_local_var_positions);
      this._setProperty("margin", v);
    } else {
      this._implicitSetter("margin", "", v, margin_export_isValid, margin_export_parse, margin_local_var_positions);
    }
  },
  get() {
    const val = this._implicitGetter("margin", margin_local_var_positions);
    if (val === "") {
      return this.getPropertyValue("margin");
    }
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var marginBottom_export_parse, marginBottom_export_isValid, marginBottom_export_definition;
marginBottom_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
marginBottom_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof marginBottom_export_parse(v) === "string";
};
marginBottom_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("margin", "");
      this._setProperty("margin-bottom", v);
    } else {
      this._subImplicitSetter("margin", "bottom", v, marginBottom_export_isValid, marginBottom_export_parse, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    return this.getPropertyValue("margin-bottom");
  },
  enumerable: true,
  configurable: true
};
var marginLeft_export_parse, marginLeft_export_isValid, marginLeft_export_definition;
marginLeft_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
marginLeft_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof marginLeft_export_parse(v) === "string";
};
marginLeft_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("margin", "");
      this._setProperty("margin-left", v);
    } else {
      this._subImplicitSetter("margin", "left", v, marginLeft_export_isValid, marginLeft_export_parse, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    return this.getPropertyValue("margin-left");
  },
  enumerable: true,
  configurable: true
};
var marginRight_export_parse, marginRight_export_isValid, marginRight_export_definition;
marginRight_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
marginRight_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof marginRight_export_parse(v) === "string";
};
marginRight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("margin", "");
      this._setProperty("margin-right", v);
    } else {
      this._subImplicitSetter("margin", "right", v, marginRight_export_isValid, marginRight_export_parse, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    return this.getPropertyValue("margin-right");
  },
  enumerable: true,
  configurable: true
};
var marginTop_export_parse, marginTop_export_isValid, marginTop_export_definition;
marginTop_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
marginTop_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof marginTop_export_parse(v) === "string";
};
marginTop_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("margin", "");
      this._setProperty("margin-top", v);
    } else {
      this._subImplicitSetter("margin", "top", v, marginTop_export_isValid, marginTop_export_parse, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    return this.getPropertyValue("margin-top");
  },
  enumerable: true,
  configurable: true
};
var opacity_export_parse, opacity_export_isValid, opacity_export_definition;
opacity_export_parse = function parse(v) {
  let num = external_dependency_parsers_0.parseNumber(v);
  if (num) {
    num = parseFloat(num);
    if (num < 0) {
      return "0";
    } else if (num > 1) {
      return "1";
    }
    return `${num}`;
  }
  let pct = external_dependency_parsers_0.parsePercent(v);
  if (pct) {
    pct = parseFloat(pct);
    if (pct < 0) {
      return "0%";
    } else if (pct > 100) {
      return "100%";
    }
    return `${pct}%`;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
opacity_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof opacity_export_parse(v) === "string";
};
opacity_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("opacity", opacity_export_parse(v));
  },
  get() {
    return this.getPropertyValue("opacity");
  },
  enumerable: true,
  configurable: true
};
var outlineColor_export_parse, outlineColor_export_isValid, outlineColor_export_definition;
outlineColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
outlineColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
outlineColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("outline-color", outlineColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("outline-color");
  },
  enumerable: true,
  configurable: true
};
var padding_export_parse, padding_export_isValid, padding_export_definition;
const padding_local_var_positions = ["top", "right", "bottom", "left"];
padding_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v, true);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
padding_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof padding_export_parse(v) === "string";
};
padding_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._implicitSetter("padding", "", "", padding_export_isValid, padding_export_parse, padding_local_var_positions);
      this._setProperty("padding", v);
    } else {
      this._implicitSetter("padding", "", v, padding_export_isValid, padding_export_parse, padding_local_var_positions);
    }
  },
  get() {
    const val = this._implicitGetter("padding", padding_local_var_positions);
    if (val === "") {
      return this.getPropertyValue("padding");
    }
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return "";
    }
    return val;
  },
  enumerable: true,
  configurable: true
};
var paddingBottom_export_parse, paddingBottom_export_isValid, paddingBottom_export_definition;
paddingBottom_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v, true);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
paddingBottom_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof paddingBottom_export_parse(v) === "string";
};
paddingBottom_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("padding", "");
      this._setProperty("padding-bottom", v);
    } else {
      this._subImplicitSetter("padding", "bottom", v, paddingBottom_export_isValid, paddingBottom_export_parse, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    return this.getPropertyValue("padding-bottom");
  },
  enumerable: true,
  configurable: true
};
var paddingLeft_export_parse, paddingLeft_export_isValid, paddingLeft_export_definition;
paddingLeft_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v, true);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
paddingLeft_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof paddingLeft_export_parse(v) === "string";
};
paddingLeft_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("padding", "");
      this._setProperty("padding-left", v);
    } else {
      this._subImplicitSetter("padding", "left", v, paddingLeft_export_isValid, paddingLeft_export_parse, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    return this.getPropertyValue("padding-left");
  },
  enumerable: true,
  configurable: true
};
var paddingRight_export_parse, paddingRight_export_isValid, paddingRight_export_definition;
paddingRight_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v, true);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
paddingRight_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof paddingRight_export_parse(v) === "string";
};
paddingRight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("padding", "");
      this._setProperty("padding-right", v);
    } else {
      this._subImplicitSetter("padding", "right", v, paddingRight_export_isValid, paddingRight_export_parse, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    return this.getPropertyValue("padding-right");
  },
  enumerable: true,
  configurable: true
};
var paddingTop_export_parse, paddingTop_export_isValid, paddingTop_export_definition;
paddingTop_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseMeasurement(v, true);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
paddingTop_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof paddingTop_export_parse(v) === "string";
};
paddingTop_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty("padding", "");
      this._setProperty("padding-top", v);
    } else {
      this._subImplicitSetter("padding", "top", v, paddingTop_export_isValid, paddingTop_export_parse, ["top", "right", "bottom", "left"]);
    }
  },
  get() {
    return this.getPropertyValue("padding-top");
  },
  enumerable: true,
  configurable: true
};
var right_export_parse, right_export_isValid, right_export_definition;
right_export_parse = function parse(v) {
  const dim = external_dependency_parsers_0.parseMeasurement(v);
  if (dim) {
    return dim;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
right_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof right_export_parse(v) === "string";
};
right_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("right", right_export_parse(v));
  },
  get() {
    return this.getPropertyValue("right");
  },
  enumerable: true,
  configurable: true
};
var stopColor_export_parse, stopColor_export_isValid, stopColor_export_definition;
stopColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
stopColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
stopColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("stop-color", stopColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("stop-color");
  },
  enumerable: true,
  configurable: true
};
var top_export_parse, top_export_isValid, top_export_definition;
top_export_parse = function parse(v) {
  const dim = external_dependency_parsers_0.parseMeasurement(v);
  if (dim) {
    return dim;
  }
  return external_dependency_parsers_0.parseKeyword(v, ["auto"]);
};
top_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof top_export_parse(v) === "string";
};
top_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("top", top_export_parse(v));
  },
  get() {
    return this.getPropertyValue("top");
  },
  enumerable: true,
  configurable: true
};
var webkitBorderAfterColor_export_parse, webkitBorderAfterColor_export_isValid, webkitBorderAfterColor_export_definition;
webkitBorderAfterColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitBorderAfterColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitBorderAfterColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-border-after-color", webkitBorderAfterColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-border-after-color");
  },
  enumerable: true,
  configurable: true
};
var webkitBorderBeforeColor_export_parse, webkitBorderBeforeColor_export_isValid, webkitBorderBeforeColor_export_definition;
webkitBorderBeforeColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitBorderBeforeColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitBorderBeforeColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-border-before-color", webkitBorderBeforeColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-border-before-color");
  },
  enumerable: true,
  configurable: true
};
var webkitBorderEndColor_export_parse, webkitBorderEndColor_export_isValid, webkitBorderEndColor_export_definition;
webkitBorderEndColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitBorderEndColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitBorderEndColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-border-end-color", webkitBorderEndColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-border-end-color");
  },
  enumerable: true,
  configurable: true
};
var webkitBorderStartColor_export_parse, webkitBorderStartColor_export_isValid, webkitBorderStartColor_export_definition;
webkitBorderStartColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitBorderStartColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitBorderStartColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-border-start-color", webkitBorderStartColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-border-start-color");
  },
  enumerable: true,
  configurable: true
};
var webkitColumnRuleColor_export_parse, webkitColumnRuleColor_export_isValid, webkitColumnRuleColor_export_definition;
webkitColumnRuleColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitColumnRuleColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitColumnRuleColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-column-rule-color", webkitColumnRuleColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-column-rule-color");
  },
  enumerable: true,
  configurable: true
};
var webkitTapHighlightColor_export_parse, webkitTapHighlightColor_export_isValid, webkitTapHighlightColor_export_definition;
webkitTapHighlightColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitTapHighlightColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitTapHighlightColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-tap-highlight-color", webkitTapHighlightColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-tap-highlight-color");
  },
  enumerable: true,
  configurable: true
};
var webkitTextEmphasisColor_export_parse, webkitTextEmphasisColor_export_isValid, webkitTextEmphasisColor_export_definition;
webkitTextEmphasisColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitTextEmphasisColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitTextEmphasisColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-text-emphasis-color", webkitTextEmphasisColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-text-emphasis-color");
  },
  enumerable: true,
  configurable: true
};
var webkitTextFillColor_export_parse, webkitTextFillColor_export_isValid, webkitTextFillColor_export_definition;
webkitTextFillColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitTextFillColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitTextFillColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-text-fill-color", webkitTextFillColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-text-fill-color");
  },
  enumerable: true,
  configurable: true
};
var webkitTextStrokeColor_export_parse, webkitTextStrokeColor_export_isValid, webkitTextStrokeColor_export_definition;
webkitTextStrokeColor_export_parse = function parse(v) {
  const val = external_dependency_parsers_0.parseColor(v);
  if (val) {
    return val;
  }
  return external_dependency_parsers_0.parseKeyword(v);
};
webkitTextStrokeColor_export_isValid = function isValid(v) {
  if (v === "" || typeof external_dependency_parsers_0.parseKeyword(v) === "string") {
    return true;
  }
  return external_dependency_parsers_0.isValidColor(v);
};
webkitTextStrokeColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("-webkit-text-stroke-color", webkitTextStrokeColor_export_parse(v));
  },
  get() {
    return this.getPropertyValue("-webkit-text-stroke-color");
  },
  enumerable: true,
  configurable: true
};
var width_export_parse, width_export_isValid, width_export_definition;
width_export_parse = function parse(v) {
  const dim = external_dependency_parsers_0.parseMeasurement(v, true);
  if (dim) {
    return dim;
  }
  const keywords = ["auto", "min-content", "max-content", "fit-content"];
  return external_dependency_parsers_0.parseKeyword(v, keywords);
};
width_export_isValid = function isValid(v) {
  if (v === "") {
    return true;
  }
  return typeof width_export_parse(v) === "string";
};
width_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    this._setProperty("width", width_export_parse(v));
  },
  get() {
    return this.getPropertyValue("width");
  },
  enumerable: true,
  configurable: true
};
module.exports = {
  backgroundImage: backgroundImage_export_definition,
  "background-image": backgroundImage_export_definition,
  backgroundPosition: backgroundPosition_export_definition,
  "background-position": backgroundPosition_export_definition,
  backgroundRepeat: backgroundRepeat_export_definition,
  "background-repeat": backgroundRepeat_export_definition,
  backgroundAttachment: backgroundAttachment_export_definition,
  "background-attachment": backgroundAttachment_export_definition,
  backgroundColor: backgroundColor_export_definition,
  "background-color": backgroundColor_export_definition,
  background: background_export_definition,
  borderWidth: borderWidth_export_definition,
  "border-width": borderWidth_export_definition,
  borderStyle: borderStyle_export_definition,
  "border-style": borderStyle_export_definition,
  borderColor: borderColor_export_definition,
  "border-color": borderColor_export_definition,
  border: border_export_definition,
  borderTopWidth: borderTopWidth_export_definition,
  "border-top-width": borderTopWidth_export_definition,
  borderTopStyle: borderTopStyle_export_definition,
  "border-top-style": borderTopStyle_export_definition,
  borderTopColor: borderTopColor_export_definition,
  "border-top-color": borderTopColor_export_definition,
  borderBottom: borderBottom_export_definition,
  "border-bottom": borderBottom_export_definition,
  borderBottomColor: borderBottomColor_export_definition,
  "border-bottom-color": borderBottomColor_export_definition,
  borderBottomStyle: borderBottomStyle_export_definition,
  "border-bottom-style": borderBottomStyle_export_definition,
  borderBottomWidth: borderBottomWidth_export_definition,
  "border-bottom-width": borderBottomWidth_export_definition,
  borderCollapse: borderCollapse_export_definition,
  "border-collapse": borderCollapse_export_definition,
  borderLeft: borderLeft_export_definition,
  "border-left": borderLeft_export_definition,
  borderLeftColor: borderLeftColor_export_definition,
  "border-left-color": borderLeftColor_export_definition,
  borderLeftStyle: borderLeftStyle_export_definition,
  "border-left-style": borderLeftStyle_export_definition,
  borderLeftWidth: borderLeftWidth_export_definition,
  "border-left-width": borderLeftWidth_export_definition,
  borderRight: borderRight_export_definition,
  "border-right": borderRight_export_definition,
  borderRightColor: borderRightColor_export_definition,
  "border-right-color": borderRightColor_export_definition,
  borderRightStyle: borderRightStyle_export_definition,
  "border-right-style": borderRightStyle_export_definition,
  borderRightWidth: borderRightWidth_export_definition,
  "border-right-width": borderRightWidth_export_definition,
  borderSpacing: borderSpacing_export_definition,
  "border-spacing": borderSpacing_export_definition,
  borderTop: borderTop_export_definition,
  "border-top": borderTop_export_definition,
  bottom: bottom_export_definition,
  clear: clear_export_definition,
  clip: clip_export_definition,
  color: color_export_definition,
  flexGrow: flexGrow_export_definition,
  "flex-grow": flexGrow_export_definition,
  flexShrink: flexShrink_export_definition,
  "flex-shrink": flexShrink_export_definition,
  flexBasis: flexBasis_export_definition,
  "flex-basis": flexBasis_export_definition,
  flex: flex_export_definition,
  float: float_export_definition,
  floodColor: floodColor_export_definition,
  "flood-color": floodColor_export_definition,
  fontStyle: fontStyle_export_definition,
  "font-style": fontStyle_export_definition,
  fontVariant: fontVariant_export_definition,
  "font-variant": fontVariant_export_definition,
  fontWeight: fontWeight_export_definition,
  "font-weight": fontWeight_export_definition,
  fontSize: fontSize_export_definition,
  "font-size": fontSize_export_definition,
  lineHeight: lineHeight_export_definition,
  "line-height": lineHeight_export_definition,
  fontFamily: fontFamily_export_definition,
  "font-family": fontFamily_export_definition,
  font: font_export_definition,
  height: height_export_definition,
  left: left_export_definition,
  lightingColor: lightingColor_export_definition,
  "lighting-color": lightingColor_export_definition,
  margin: margin_export_definition,
  marginBottom: marginBottom_export_definition,
  "margin-bottom": marginBottom_export_definition,
  marginLeft: marginLeft_export_definition,
  "margin-left": marginLeft_export_definition,
  marginRight: marginRight_export_definition,
  "margin-right": marginRight_export_definition,
  marginTop: marginTop_export_definition,
  "margin-top": marginTop_export_definition,
  opacity: opacity_export_definition,
  outlineColor: outlineColor_export_definition,
  "outline-color": outlineColor_export_definition,
  padding: padding_export_definition,
  paddingBottom: paddingBottom_export_definition,
  "padding-bottom": paddingBottom_export_definition,
  paddingLeft: paddingLeft_export_definition,
  "padding-left": paddingLeft_export_definition,
  paddingRight: paddingRight_export_definition,
  "padding-right": paddingRight_export_definition,
  paddingTop: paddingTop_export_definition,
  "padding-top": paddingTop_export_definition,
  right: right_export_definition,
  stopColor: stopColor_export_definition,
  "stop-color": stopColor_export_definition,
  top: top_export_definition,
  webkitBorderAfterColor: webkitBorderAfterColor_export_definition,
  "-webkit-border-after-color": webkitBorderAfterColor_export_definition,
  "WebkitBorderAfterColor": webkitBorderAfterColor_export_definition,
  webkitBorderBeforeColor: webkitBorderBeforeColor_export_definition,
  "-webkit-border-before-color": webkitBorderBeforeColor_export_definition,
  "WebkitBorderBeforeColor": webkitBorderBeforeColor_export_definition,
  webkitBorderEndColor: webkitBorderEndColor_export_definition,
  "-webkit-border-end-color": webkitBorderEndColor_export_definition,
  "WebkitBorderEndColor": webkitBorderEndColor_export_definition,
  webkitBorderStartColor: webkitBorderStartColor_export_definition,
  "-webkit-border-start-color": webkitBorderStartColor_export_definition,
  "WebkitBorderStartColor": webkitBorderStartColor_export_definition,
  webkitColumnRuleColor: webkitColumnRuleColor_export_definition,
  "-webkit-column-rule-color": webkitColumnRuleColor_export_definition,
  "WebkitColumnRuleColor": webkitColumnRuleColor_export_definition,
  webkitTapHighlightColor: webkitTapHighlightColor_export_definition,
  "-webkit-tap-highlight-color": webkitTapHighlightColor_export_definition,
  "WebkitTapHighlightColor": webkitTapHighlightColor_export_definition,
  webkitTextEmphasisColor: webkitTextEmphasisColor_export_definition,
  "-webkit-text-emphasis-color": webkitTextEmphasisColor_export_definition,
  "WebkitTextEmphasisColor": webkitTextEmphasisColor_export_definition,
  webkitTextFillColor: webkitTextFillColor_export_definition,
  "-webkit-text-fill-color": webkitTextFillColor_export_definition,
  "WebkitTextFillColor": webkitTextFillColor_export_definition,
  webkitTextStrokeColor: webkitTextStrokeColor_export_definition,
  "-webkit-text-stroke-color": webkitTextStrokeColor_export_definition,
  "WebkitTextStrokeColor": webkitTextStrokeColor_export_definition,
  width: width_export_definition
};

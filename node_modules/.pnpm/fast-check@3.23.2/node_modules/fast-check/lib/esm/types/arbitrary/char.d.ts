import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
/**
 * For single printable ascii characters - char code between 0x20 (included) and 0x7e (included)
 *
 * {@link https://www.ascii-code.com/}
 *
 * @deprecated Please use ${@link string} with `fc.string({ minLength: 1, maxLength: 1 })` instead
 * @remarks Since 0.0.1
 * @public
 */
export declare function char(): Arbitrary<string>;

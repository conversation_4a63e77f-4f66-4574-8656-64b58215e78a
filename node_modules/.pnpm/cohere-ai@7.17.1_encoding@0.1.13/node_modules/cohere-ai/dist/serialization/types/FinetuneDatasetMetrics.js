"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinetuneDatasetMetrics = void 0;
const core = __importStar(require("../../core"));
exports.FinetuneDatasetMetrics = core.serialization.object({
    trainableTokenCount: core.serialization.property("trainable_token_count", core.serialization.number().optional()),
    totalExamples: core.serialization.property("total_examples", core.serialization.number().optional()),
    trainExamples: core.serialization.property("train_examples", core.serialization.number().optional()),
    trainSizeBytes: core.serialization.property("train_size_bytes", core.serialization.number().optional()),
    evalExamples: core.serialization.property("eval_examples", core.serialization.number().optional()),
    evalSizeBytes: core.serialization.property("eval_size_bytes", core.serialization.number().optional()),
});

#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Dropbox/madhukar/robynnv3/code/robynnv3/node_modules/.pnpm/why-is-node-running@2.3.0/node_modules/why-is-node-running/node_modules:/Users/<USER>/Dropbox/madhukar/robynnv3/code/robynnv3/node_modules/.pnpm/why-is-node-running@2.3.0/node_modules:/Users/<USER>/Dropbox/madhukar/robynnv3/code/robynnv3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Dropbox/madhukar/robynnv3/code/robynnv3/node_modules/.pnpm/why-is-node-running@2.3.0/node_modules/why-is-node-running/node_modules:/Users/<USER>/Dropbox/madhukar/robynnv3/code/robynnv3/node_modules/.pnpm/why-is-node-running@2.3.0/node_modules:/Users/<USER>/Dropbox/madhukar/robynnv3/code/robynnv3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../why-is-node-running/cli.js" "$@"
else
  exec node  "$basedir/../../../why-is-node-running/cli.js" "$@"
fi

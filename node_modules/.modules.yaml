hoistPattern:
  - '*'
hoistedDependencies:
  '@ai-sdk/provider-utils@2.2.8(zod@3.25.67)':
    '@ai-sdk/provider-utils': private
  '@ai-sdk/provider@1.1.3':
    '@ai-sdk/provider': private
  '@ai-sdk/react@1.2.12(react@18.3.1)(zod@3.25.67)':
    '@ai-sdk/react': private
  '@ai-sdk/ui-utils@1.2.11(zod@3.25.67)':
    '@ai-sdk/ui-utils': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@apidevtools/json-schema-ref-parser@11.9.3':
    '@apidevtools/json-schema-ref-parser': private
  '@ark/schema@0.46.0':
    '@ark/schema': private
  '@ark/util@0.46.0':
    '@ark/util': private
  '@asamuzakjp/css-color@3.2.0':
    '@asamuzakjp/css-color': private
  '@aws-crypto/crc32@3.0.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/sha256-browser@5.2.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@5.2.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@5.2.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@5.2.0':
    '@aws-crypto/util': private
  '@aws-sdk/client-cognito-identity@3.840.0':
    '@aws-sdk/client-cognito-identity': private
  '@aws-sdk/client-sagemaker@3.843.0':
    '@aws-sdk/client-sagemaker': private
  '@aws-sdk/client-sso@3.840.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/core@3.840.0':
    '@aws-sdk/core': private
  '@aws-sdk/credential-provider-cognito-identity@3.840.0':
    '@aws-sdk/credential-provider-cognito-identity': private
  '@aws-sdk/credential-provider-env@3.840.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-http@3.840.0':
    '@aws-sdk/credential-provider-http': private
  '@aws-sdk/credential-provider-ini@3.840.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.840.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.840.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.840.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.840.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/credential-providers@3.840.0':
    '@aws-sdk/credential-providers': private
  '@aws-sdk/middleware-host-header@3.840.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-logger@3.840.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.840.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-user-agent@3.840.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/nested-clients@3.840.0':
    '@aws-sdk/nested-clients': private
  '@aws-sdk/protocol-http@3.374.0':
    '@aws-sdk/protocol-http': private
  '@aws-sdk/region-config-resolver@3.840.0':
    '@aws-sdk/region-config-resolver': private
  '@aws-sdk/signature-v4@3.374.0':
    '@aws-sdk/signature-v4': private
  '@aws-sdk/token-providers@3.840.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.840.0':
    '@aws-sdk/types': private
  '@aws-sdk/util-endpoints@3.840.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-locate-window@3.804.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-user-agent-browser@3.840.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.840.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/util-utf8-browser@3.259.0':
    '@aws-sdk/util-utf8-browser': private
  '@aws-sdk/xml-builder@3.821.0':
    '@aws-sdk/xml-builder': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@csstools/color-helpers@5.0.2':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': private
  '@egjs/hammerjs@2.0.17':
    '@egjs/hammerjs': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@exodus/schemasafe@1.3.0':
    '@exodus/schemasafe': private
  '@floating-ui/core@1.7.2':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.2':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@gcornut/valibot-json-schema@0.42.0(esbuild@0.25.6)(typescript@5.8.3)':
    '@gcornut/valibot-json-schema': private
  '@grpc/grpc-js@1.13.4':
    '@grpc/grpc-js': private
  '@grpc/proto-loader@0.7.15':
    '@grpc/proto-loader': private
  '@hapi/hoek@9.3.0':
    '@hapi/hoek': private
  '@hapi/topo@5.1.0':
    '@hapi/topo': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@internationalized/date@3.8.2':
    '@internationalized/date': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@js-sdsl/ordered-map@4.4.2':
    '@js-sdsl/ordered-map': private
  '@jsdevtools/ono@7.1.3':
    '@jsdevtools/ono': private
  '@mapbox/node-pre-gyp@2.0.0(encoding@0.1.13)':
    '@mapbox/node-pre-gyp': private
  '@mastra/schema-compat@0.10.3(ai@4.3.16(react@18.3.1)(zod@3.25.67))(zod@3.25.67)':
    '@mastra/schema-compat': private
  '@melt-ui/svelte@0.76.2(svelte@5.34.9)':
    '@melt-ui/svelte': private
  '@motionone/animation@10.18.0':
    '@motionone/animation': private
  '@motionone/dom@10.18.0':
    '@motionone/dom': private
  '@motionone/easing@10.18.0':
    '@motionone/easing': private
  '@motionone/generators@10.18.0':
    '@motionone/generators': private
  '@motionone/types@10.17.1':
    '@motionone/types': private
  '@motionone/utils@10.18.0':
    '@motionone/utils': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@one-ini/wasm@0.1.1':
    '@one-ini/wasm': private
  '@opentelemetry/api-logs@0.201.1':
    '@opentelemetry/api-logs': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@opentelemetry/auto-instrumentations-node@0.59.0(@opentelemetry/api@1.9.0)(@opentelemetry/core@2.0.1(@opentelemetry/api@1.9.0))(encoding@0.1.13)':
    '@opentelemetry/auto-instrumentations-node': private
  '@opentelemetry/context-async-hooks@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/context-async-hooks': private
  '@opentelemetry/core@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/core': private
  '@opentelemetry/exporter-logs-otlp-grpc@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-logs-otlp-grpc': private
  '@opentelemetry/exporter-logs-otlp-http@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-logs-otlp-http': private
  '@opentelemetry/exporter-logs-otlp-proto@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-logs-otlp-proto': private
  '@opentelemetry/exporter-metrics-otlp-grpc@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-metrics-otlp-grpc': private
  '@opentelemetry/exporter-metrics-otlp-http@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-metrics-otlp-http': private
  '@opentelemetry/exporter-metrics-otlp-proto@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-metrics-otlp-proto': private
  '@opentelemetry/exporter-prometheus@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-prometheus': private
  '@opentelemetry/exporter-trace-otlp-grpc@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-trace-otlp-grpc': private
  '@opentelemetry/exporter-trace-otlp-http@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-trace-otlp-http': private
  '@opentelemetry/exporter-trace-otlp-proto@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-trace-otlp-proto': private
  '@opentelemetry/exporter-zipkin@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-zipkin': private
  '@opentelemetry/instrumentation-amqplib@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-amqplib': private
  '@opentelemetry/instrumentation-aws-lambda@0.52.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-aws-lambda': private
  '@opentelemetry/instrumentation-aws-sdk@0.53.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-aws-sdk': private
  '@opentelemetry/instrumentation-bunyan@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-bunyan': private
  '@opentelemetry/instrumentation-cassandra-driver@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-cassandra-driver': private
  '@opentelemetry/instrumentation-connect@0.45.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-connect': private
  '@opentelemetry/instrumentation-cucumber@0.16.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-cucumber': private
  '@opentelemetry/instrumentation-dataloader@0.18.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-dataloader': private
  '@opentelemetry/instrumentation-dns@0.45.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-dns': private
  '@opentelemetry/instrumentation-express@0.50.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-express': private
  '@opentelemetry/instrumentation-fastify@0.46.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-fastify': private
  '@opentelemetry/instrumentation-fs@0.21.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-fs': private
  '@opentelemetry/instrumentation-generic-pool@0.45.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-generic-pool': private
  '@opentelemetry/instrumentation-graphql@0.49.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-graphql': private
  '@opentelemetry/instrumentation-grpc@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-grpc': private
  '@opentelemetry/instrumentation-hapi@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-hapi': private
  '@opentelemetry/instrumentation-http@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-http': private
  '@opentelemetry/instrumentation-ioredis@0.49.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-ioredis': private
  '@opentelemetry/instrumentation-kafkajs@0.10.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-kafkajs': private
  '@opentelemetry/instrumentation-knex@0.46.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-knex': private
  '@opentelemetry/instrumentation-koa@0.49.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-koa': private
  '@opentelemetry/instrumentation-lru-memoizer@0.46.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-lru-memoizer': private
  '@opentelemetry/instrumentation-memcached@0.45.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-memcached': private
  '@opentelemetry/instrumentation-mongodb@0.54.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongodb': private
  '@opentelemetry/instrumentation-mongoose@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongoose': private
  '@opentelemetry/instrumentation-mysql2@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql2': private
  '@opentelemetry/instrumentation-mysql@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql': private
  '@opentelemetry/instrumentation-nestjs-core@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-nestjs-core': private
  '@opentelemetry/instrumentation-net@0.45.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-net': private
  '@opentelemetry/instrumentation-oracledb@0.27.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-oracledb': private
  '@opentelemetry/instrumentation-pg@0.53.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-pg': private
  '@opentelemetry/instrumentation-pino@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-pino': private
  '@opentelemetry/instrumentation-redis-4@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-redis-4': private
  '@opentelemetry/instrumentation-redis@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-redis': private
  '@opentelemetry/instrumentation-restify@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-restify': private
  '@opentelemetry/instrumentation-router@0.46.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-router': private
  '@opentelemetry/instrumentation-runtime-node@0.15.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-runtime-node': private
  '@opentelemetry/instrumentation-socket.io@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-socket.io': private
  '@opentelemetry/instrumentation-tedious@0.20.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-tedious': private
  '@opentelemetry/instrumentation-undici@0.12.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-undici': private
  '@opentelemetry/instrumentation-winston@0.46.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-winston': private
  '@opentelemetry/instrumentation@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation': private
  '@opentelemetry/otlp-exporter-base@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/otlp-exporter-base': private
  '@opentelemetry/otlp-grpc-exporter-base@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/otlp-grpc-exporter-base': private
  '@opentelemetry/otlp-transformer@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/otlp-transformer': private
  '@opentelemetry/propagation-utils@0.31.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/propagation-utils': private
  '@opentelemetry/propagator-b3@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/propagator-b3': private
  '@opentelemetry/propagator-jaeger@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/propagator-jaeger': private
  '@opentelemetry/redis-common@0.37.0':
    '@opentelemetry/redis-common': private
  '@opentelemetry/resource-detector-alibaba-cloud@0.31.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resource-detector-alibaba-cloud': private
  '@opentelemetry/resource-detector-aws@2.2.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resource-detector-aws': private
  '@opentelemetry/resource-detector-azure@0.8.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resource-detector-azure': private
  '@opentelemetry/resource-detector-container@0.7.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resource-detector-container': private
  '@opentelemetry/resource-detector-gcp@0.35.0(@opentelemetry/api@1.9.0)(encoding@0.1.13)':
    '@opentelemetry/resource-detector-gcp': private
  '@opentelemetry/resources@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resources': private
  '@opentelemetry/sdk-logs@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-logs': private
  '@opentelemetry/sdk-metrics@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-metrics': private
  '@opentelemetry/sdk-node@0.201.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-node': private
  '@opentelemetry/sdk-trace-base@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-base': private
  '@opentelemetry/sdk-trace-node@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-node': private
  '@opentelemetry/semantic-conventions@1.34.0':
    '@opentelemetry/semantic-conventions': private
  '@opentelemetry/sql-common@0.41.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sql-common': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@poppinss/macroable@1.0.4':
    '@poppinss/macroable': private
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': private
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': private
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': private
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': private
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': private
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': private
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': private
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': private
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': private
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-compose-refs@1.1.2(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dismissable-layer@1.1.10(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-hover-card@1.1.14(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-hover-card': private
  '@radix-ui/react-popper@1.2.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-slot@1.2.3(react@18.3.1)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-use-callback-ref@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(react@18.3.1)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-rect@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@react-email/render@0.0.16(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@react-email/render': private
  '@rollup/pluginutils@5.2.0(rollup@4.44.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.44.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@selderee/plugin-htmlparser2@0.11.0':
    '@selderee/plugin-htmlparser2': private
  '@sideway/address@4.1.5':
    '@sideway/address': private
  '@sideway/formula@3.0.1':
    '@sideway/formula': private
  '@sideway/pinpoint@2.0.0':
    '@sideway/pinpoint': private
  '@sinclair/typebox@0.34.37':
    '@sinclair/typebox': private
  '@sindresorhus/slugify@2.2.1':
    '@sindresorhus/slugify': private
  '@sindresorhus/transliterate@1.6.0':
    '@sindresorhus/transliterate': private
  '@smithy/abort-controller@4.0.4':
    '@smithy/abort-controller': private
  '@smithy/config-resolver@4.1.4':
    '@smithy/config-resolver': private
  '@smithy/core@3.6.0':
    '@smithy/core': private
  '@smithy/credential-provider-imds@4.0.6':
    '@smithy/credential-provider-imds': private
  '@smithy/eventstream-codec@1.1.0':
    '@smithy/eventstream-codec': private
  '@smithy/fetch-http-handler@5.0.4':
    '@smithy/fetch-http-handler': private
  '@smithy/hash-node@4.0.4':
    '@smithy/hash-node': private
  '@smithy/invalid-dependency@4.0.4':
    '@smithy/invalid-dependency': private
  '@smithy/is-array-buffer@1.1.0':
    '@smithy/is-array-buffer': private
  '@smithy/middleware-content-length@4.0.4':
    '@smithy/middleware-content-length': private
  '@smithy/middleware-endpoint@4.1.13':
    '@smithy/middleware-endpoint': private
  '@smithy/middleware-retry@4.1.14':
    '@smithy/middleware-retry': private
  '@smithy/middleware-serde@4.0.8':
    '@smithy/middleware-serde': private
  '@smithy/middleware-stack@4.0.4':
    '@smithy/middleware-stack': private
  '@smithy/node-config-provider@4.1.3':
    '@smithy/node-config-provider': private
  '@smithy/node-http-handler@4.0.6':
    '@smithy/node-http-handler': private
  '@smithy/property-provider@4.0.4':
    '@smithy/property-provider': private
  '@smithy/protocol-http@5.1.2':
    '@smithy/protocol-http': private
  '@smithy/querystring-builder@4.0.4':
    '@smithy/querystring-builder': private
  '@smithy/querystring-parser@4.0.4':
    '@smithy/querystring-parser': private
  '@smithy/service-error-classification@4.0.6':
    '@smithy/service-error-classification': private
  '@smithy/shared-ini-file-loader@4.0.4':
    '@smithy/shared-ini-file-loader': private
  '@smithy/signature-v4@1.1.0':
    '@smithy/signature-v4': private
  '@smithy/smithy-client@4.4.5':
    '@smithy/smithy-client': private
  '@smithy/types@4.3.1':
    '@smithy/types': private
  '@smithy/url-parser@4.0.4':
    '@smithy/url-parser': private
  '@smithy/util-base64@4.0.0':
    '@smithy/util-base64': private
  '@smithy/util-body-length-browser@4.0.0':
    '@smithy/util-body-length-browser': private
  '@smithy/util-body-length-node@4.0.0':
    '@smithy/util-body-length-node': private
  '@smithy/util-buffer-from@4.0.0':
    '@smithy/util-buffer-from': private
  '@smithy/util-config-provider@4.0.0':
    '@smithy/util-config-provider': private
  '@smithy/util-defaults-mode-browser@4.0.21':
    '@smithy/util-defaults-mode-browser': private
  '@smithy/util-defaults-mode-node@4.0.21':
    '@smithy/util-defaults-mode-node': private
  '@smithy/util-endpoints@3.0.6':
    '@smithy/util-endpoints': private
  '@smithy/util-hex-encoding@1.1.0':
    '@smithy/util-hex-encoding': private
  '@smithy/util-middleware@4.0.4':
    '@smithy/util-middleware': private
  '@smithy/util-retry@4.0.6':
    '@smithy/util-retry': private
  '@smithy/util-stream@4.2.2':
    '@smithy/util-stream': private
  '@smithy/util-uri-escape@1.1.0':
    '@smithy/util-uri-escape': private
  '@smithy/util-utf8@4.0.0':
    '@smithy/util-utf8': private
  '@smithy/util-waiter@4.0.6':
    '@smithy/util-waiter': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@stitches/core@1.2.8':
    '@stitches/core': private
  '@supabase/auth-js@2.70.0':
    '@supabase/auth-js': private
  '@supabase/functions-js@2.4.4':
    '@supabase/functions-js': private
  '@supabase/node-fetch@2.6.15':
    '@supabase/node-fetch': private
  '@supabase/postgrest-js@1.19.4':
    '@supabase/postgrest-js': private
  '@supabase/realtime-js@2.11.15(bufferutil@4.0.9)(utf-8-validate@6.0.5)':
    '@supabase/realtime-js': private
  '@supabase/storage-js@2.7.1':
    '@supabase/storage-js': private
  '@sveltejs/acorn-typescript@1.0.5(acorn@8.15.0)':
    '@sveltejs/acorn-typescript': private
  '@sveltejs/vite-plugin-svelte-inspector@3.0.1(@sveltejs/vite-plugin-svelte@4.0.4(svelte@5.34.9)(vite@5.4.19(@types/node@24.0.10)(lightningcss@1.30.1)))(svelte@5.34.9)(vite@5.4.19(@types/node@24.0.10)(lightningcss@1.30.1))':
    '@sveltejs/vite-plugin-svelte-inspector': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.11':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.11':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@tanstack/query-core@5.83.0':
    '@tanstack/query-core': private
  '@tanstack/react-query@5.83.0(react@18.3.1)':
    '@tanstack/react-query': private
  '@types/aws-lambda@8.10.147':
    '@types/aws-lambda': private
  '@types/bunyan@1.8.11':
    '@types/bunyan': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/diff-match-patch@1.0.36':
    '@types/diff-match-patch': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/hammerjs@2.0.46':
    '@types/hammerjs': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/memcached@2.2.10':
    '@types/memcached': private
  '@types/minimatch@5.1.2':
    '@types/minimatch': private
  '@types/mysql@2.15.26':
    '@types/mysql': private
  '@types/node@24.0.10':
    '@types/node': private
  '@types/oracledb@6.5.2':
    '@types/oracledb': private
  '@types/pg-pool@2.0.6':
    '@types/pg-pool': private
  '@types/pg@8.6.1':
    '@types/pg': private
  '@types/phoenix@1.6.6':
    '@types/phoenix': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/shimmer@1.2.0':
    '@types/shimmer': private
  '@types/tedious@4.0.14':
    '@types/tedious': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/uuid@9.0.8':
    '@types/uuid': private
  '@types/validator@13.15.2':
    '@types/validator': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@typeschema/class-validator@0.3.0(@types/json-schema@7.0.15)(class-validator@0.14.2)':
    '@typeschema/class-validator': private
  '@typeschema/core@0.14.0(@types/json-schema@7.0.15)':
    '@typeschema/core': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vercel/nft@0.29.4(encoding@0.1.13)(rollup@4.44.1)':
    '@vercel/nft': private
  '@vinejs/compiler@3.0.0':
    '@vinejs/compiler': private
  '@vinejs/vine@3.0.1':
    '@vinejs/vine': private
  '@vitest/expect@1.6.1':
    '@vitest/expect': private
  '@vitest/runner@1.6.1':
    '@vitest/runner': private
  '@vitest/snapshot@1.6.1':
    '@vitest/snapshot': private
  '@vitest/spy@1.6.1':
    '@vitest/spy': private
  '@vitest/utils@1.6.1':
    '@vitest/utils': private
  abbrev@2.0.0:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  acorn-import-attributes@1.9.5(acorn@8.15.0):
    acorn-import-attributes: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  ai@4.3.16(react@18.3.1)(zod@3.25.67):
    ai: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@1.0.10:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  arktype@2.1.20:
    arktype: private
  array-union@2.1.0:
    array-union: private
  assertion-error@1.1.0:
    assertion-error: private
  async-sema@3.1.1:
    async-sema: private
  asynckit@0.4.0:
    asynckit: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  axobject-query@4.1.0:
    axobject-query: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bignumber.js@9.3.0:
    bignumber.js: private
  bindings@1.5.0:
    bindings: private
  bowser@2.11.0:
    bowser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@8.0.0:
    camelcase: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  chai@4.5.0:
    chai: private
  chalk@4.1.2:
    chalk: private
  check-error@1.0.3:
    check-error: private
  chokidar@4.0.3:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  class-validator@0.14.2:
    class-validator: private
  cliui@8.0.1:
    cliui: private
  clone@2.1.2:
    clone: private
  cohere-ai@7.17.1(encoding@0.1.13):
    cohere-ai: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@10.0.1:
    commander: private
  component-emitter@2.0.0:
    component-emitter: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  config-chain@1.1.13:
    config-chain: private
  consola@3.4.2:
    consola: private
  convict@6.2.4:
    convict: private
  cookie@0.7.2:
    cookie: private
  cross-fetch@4.1.0(encoding@0.1.13):
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  cssstyle@4.6.0:
    cssstyle: private
  data-urls@5.0.0:
    data-urls: private
  date-fns@3.6.0:
    date-fns: private
  dateformat@4.6.3:
    dateformat: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  decimal.js@10.5.0:
    decimal.js: private
  deep-eql@4.1.4:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  devalue@5.1.1:
    devalue: private
  diff-match-patch@1.0.5:
    diff-match-patch: private
  diff-sequences@29.6.3:
    diff-sequences: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  doctrine@3.0.0:
    doctrine: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dotenv@16.6.1:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  editorconfig@1.0.4:
    editorconfig: private
  effect@3.16.10:
    effect: private
  electron-to-chromium@1.5.178:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild-runner@2.2.2(esbuild@0.25.6):
    esbuild-runner: private
  esbuild@0.25.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-compat-utils@0.5.1(eslint@8.57.1):
    eslint-compat-utils: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  esm-env@1.2.2:
    esm-env: private
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrap@1.4.9:
    esrap: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  event-target-shim@5.0.1:
    event-target-shim: private
  events@3.3.0:
    events: private
  execa@8.0.1:
    execa: private
  extend-shallow@2.0.1:
    extend-shallow: private
  extend@3.0.2:
    extend: private
  fast-check@3.23.2:
    fast-check: private
  fast-copy@3.0.2:
    fast-copy: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-redact@3.5.0:
    fast-redact: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-xml-parser@4.4.1:
    fast-xml-parser: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-uri-to-path@1.0.0:
    file-uri-to-path: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  focus-trap@7.6.5:
    focus-trap: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data-encoder@4.1.0:
    form-data-encoder: private
  form-data@4.0.3:
    form-data: private
  formdata-node@6.0.3:
    formdata-node: private
  forwarded-parse@2.1.2:
    forwarded-parse: private
  fraction.js@4.3.7:
    fraction.js: private
  framer-motion@12.23.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    framer-motion: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gaxios@6.7.1(encoding@0.1.13):
    gaxios: private
  gcp-metadata@6.1.1(encoding@0.1.13):
    gcp-metadata: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-func-name@2.0.2:
    get-func-name: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@8.0.1:
    get-stream: private
  glob-parent@6.0.2:
    glob-parent: private
  globals@13.24.0:
    globals: private
  globby@11.1.0:
    globby: private
  google-logging-utils@0.0.2:
    google-logging-utils: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  help-me@5.0.0:
    help-me: private
  hey-listen@1.0.8:
    hey-listen: private
  hono-openapi@0.4.8(@sinclair/typebox@0.34.37)(arktype@2.1.20)(effect@3.16.10)(hono@4.8.4)(openapi-types@12.1.3)(valibot@1.1.0(typescript@5.8.3))(zod@3.25.67):
    hono-openapi: private
  hono@4.8.4:
    hono: private
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: private
  htmlparser2@8.0.2:
    htmlparser2: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@5.0.0:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-in-the-middle@1.14.2:
    import-in-the-middle: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-reference@3.0.3:
    is-reference: private
  is-stream@3.0.0:
    is-stream: private
  isexe@2.0.0:
    isexe: private
  isows@1.0.7(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5)):
    isows: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@2.4.2:
    jiti: private
  joi@17.13.3:
    joi: private
  joycon@3.1.1:
    joycon: private
  js-base64@3.7.2:
    js-base64: private
  js-beautify@1.15.4:
    js-beautify: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@9.0.1:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-bigint@1.0.0:
    json-bigint: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-to-ts@3.1.1:
    json-schema-to-ts: private
  json-schema-to-zod@2.6.1:
    json-schema-to-zod: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema-walker@2.0.0:
    json-schema-walker: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  jsondiffpatch@0.6.0:
    jsondiffpatch: private
  keycharm@0.4.0:
    keycharm: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kleur@4.1.5:
    kleur: private
  known-css-properties@0.35.0:
    known-css-properties: private
  leac@0.6.0:
    leac: private
  levn@0.4.1:
    levn: private
  libphonenumber-js@1.12.9:
    libphonenumber-js: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lilconfig@2.1.0:
    lilconfig: private
  local-pkg@0.5.1:
    local-pkg: private
  locate-character@3.0.0:
    locate-character: private
  locate-path@6.0.0:
    locate-path: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.clonedeep@4.5.0:
    lodash.clonedeep: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  long@5.3.2:
    long: private
  loose-envify@1.4.0:
    loose-envify: private
  loupe@2.3.7:
    loupe: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  memoize-weak@1.0.2:
    memoize-weak: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@4.0.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@4.2.8:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  module-details-from-path@1.0.4:
    module-details-from-path: private
  motion-dom@12.23.2:
    motion-dom: private
  motion-utils@12.23.2:
    motion-utils: private
  mri@1.2.0:
    mri: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  nanoid@5.1.5:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-fetch@2.7.0(encoding@0.1.13):
    node-fetch: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-releases@2.0.19:
    node-releases: private
  nopt@8.1.0:
    nopt: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@8.0.2:
    normalize-url: private
  npm-run-path@5.3.0:
    npm-run-path: private
  nwsapi@2.2.20:
    nwsapi: private
  object-inspect@1.13.4:
    object-inspect: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  once@1.4.0:
    once: private
  onetime@6.0.0:
    onetime: private
  openai@5.8.2(ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5))(zod@3.25.67):
    openai: private
  openapi-types@12.1.3:
    openapi-types: private
  optionator@0.9.4:
    optionator: private
  p-limit@5.0.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse5@7.3.0:
    parse5: private
  parseley@0.12.1:
    parseley: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  pathval@1.1.1:
    pathval: private
  peberminta@0.9.0:
    peberminta: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pino-abstract-transport@2.0.0:
    pino-abstract-transport: private
  pino-pretty@13.0.0:
    pino-pretty: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pino@9.7.0:
    pino: private
  pkg-types@1.3.1:
    pkg-types: private
  postcss-load-config@3.1.4(postcss@8.5.6):
    postcss-load-config: private
  postcss-safe-parser@6.0.0(postcss@8.5.6):
    postcss-safe-parser: private
  postcss-scss@4.0.9(postcss@8.5.6):
    postcss-scss: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@29.7.0:
    pretty-format: private
  process-warning@5.0.0:
    process-warning: private
  process@0.11.10:
    process: private
  property-expr@2.0.6:
    property-expr: private
  proto-list@1.2.4:
    proto-list: private
  protobufjs@7.5.3:
    protobufjs: private
  psl@1.15.0:
    psl: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.14.0:
    qs: private
  qss@3.0.0:
    qss: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  radash@12.1.1:
    radash: private
  react-dom@18.3.1(react@18.3.1):
    react-dom: private
  react-is@18.3.1:
    react-is: private
  react-promise-suspense@0.3.4:
    react-promise-suspense: private
  react@18.3.1:
    react: private
  readable-stream@4.7.0:
    readable-stream: private
  readdirp@4.1.2:
    readdirp: private
  real-require@0.2.0:
    real-require: private
  require-directory@2.1.1:
    require-directory: private
  require-in-the-middle@7.5.2:
    require-in-the-middle: private
  requires-port@1.0.0:
    requires-port: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.44.1:
    rollup: private
  rrweb-cssom@0.7.1:
    rrweb-cssom: private
  run-parallel@1.2.0:
    run-parallel: private
  sade@1.8.1:
    sade: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@6.0.0:
    saxes: private
  scheduler@0.23.2:
    scheduler: private
  section-matter@1.0.0:
    section-matter: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  selderee@0.11.0:
    selderee: private
  semver@7.7.2:
    semver: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shimmer@1.2.1:
    shimmer: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  sift@17.1.3:
    sift: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  sirv@3.0.1:
    sirv: private
  slash@3.0.0:
    slash: private
  sonic-boom@4.2.0:
    sonic-boom: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stackback@0.0.2:
    stackback: private
  std-env@3.9.0:
    std-env: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom-string@1.0.0:
    strip-bom-string: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@2.1.1:
    strip-literal: private
  strnum@1.1.2:
    strnum: private
  superstruct@2.0.2:
    superstruct: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svelte-eslint-parser@0.43.0(svelte@5.34.9):
    svelte-eslint-parser: private
  swr@2.3.4(react@18.3.1):
    swr: private
  symbol-tree@3.2.4:
    symbol-tree: private
  tabbable@6.2.0:
    tabbable: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  text-table@0.2.0:
    text-table: private
  thread-stream@3.1.0:
    thread-stream: private
  throttleit@2.1.0:
    throttleit: private
  tiny-case@1.0.3:
    tiny-case: private
  tinybench@2.9.0:
    tinybench: private
  tinypool@0.8.4:
    tinypool: private
  tinyspy@2.2.1:
    tinyspy: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toposort@2.0.2:
    toposort: private
  totalist@3.0.1:
    totalist: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@5.1.1:
    tr46: private
  ts-algebra@2.0.0:
    ts-algebra: private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  ts-deepmerge@7.0.3:
    ts-deepmerge: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.1.0:
    type-detect: private
  type-fest@0.20.2:
    type-fest: private
  ufo@1.6.1:
    ufo: private
  undici-types@7.8.0:
    undici-types: private
  universalify@0.2.0:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-join@4.0.1:
    url-join: private
  url-parse@1.5.10:
    url-parse: private
  use-sync-external-store@1.5.0(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@9.0.1:
    uuid: private
  valibot@1.1.0(typescript@5.8.3):
    valibot: private
  validator@13.15.15:
    validator: private
  vis-data@8.0.1(uuid@9.0.1)(vis-util@6.0.0(@egjs/hammerjs@2.0.17)(component-emitter@2.0.0)):
    vis-data: private
  vis-util@6.0.0(@egjs/hammerjs@2.0.17)(component-emitter@2.0.0):
    vis-util: private
  vite-node@1.6.1(@types/node@24.0.10)(lightningcss@1.30.1):
    vite-node: private
  vitefu@1.0.7(vite@5.4.19(@types/node@24.0.10)(lightningcss@1.30.1)):
    vitefu: private
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@14.2.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.3(bufferutil@4.0.9)(utf-8-validate@6.0.5):
    ws: private
  xml-name-validator@5.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
  xstate@5.20.1:
    xstate: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@20.2.9:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@1.2.1:
    yocto-queue: private
  yup@1.6.1:
    yup: private
  zimmerframe@1.1.2:
    zimmerframe: private
  zod-from-json-schema@0.0.5:
    zod-from-json-schema: private
  zod-to-json-schema@3.24.6(zod@3.25.67):
    zod-to-json-schema: private
ignoredBuilds:
  - esbuild
  - '@tailwindcss/oxide'
  - protobufjs
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Tue, 15 Jul 2025 03:29:45 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.6'
  - '@esbuild/win32-x64@0.21.5'
  - '@esbuild/win32-x64@0.25.6'
  - '@rollup/rollup-android-arm-eabi@4.44.1'
  - '@rollup/rollup-android-arm64@4.44.1'
  - '@rollup/rollup-darwin-x64@4.44.1'
  - '@rollup/rollup-freebsd-arm64@4.44.1'
  - '@rollup/rollup-freebsd-x64@4.44.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.1'
  - '@rollup/rollup-linux-arm64-gnu@4.44.1'
  - '@rollup/rollup-linux-arm64-musl@4.44.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-musl@4.44.1'
  - '@rollup/rollup-linux-s390x-gnu@4.44.1'
  - '@rollup/rollup-linux-x64-gnu@4.44.1'
  - '@rollup/rollup-linux-x64-musl@4.44.1'
  - '@rollup/rollup-win32-arm64-msvc@4.44.1'
  - '@rollup/rollup-win32-ia32-msvc@4.44.1'
  - '@rollup/rollup-win32-x64-msvc@4.44.1'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.11'
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120

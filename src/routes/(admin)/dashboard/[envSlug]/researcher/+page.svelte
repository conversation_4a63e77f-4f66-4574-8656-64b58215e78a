<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import { onMount } from "svelte"
  import { slide, fade } from "svelte/transition"
  import {
    Search,
    Download,
    Building2,
    TrendingUp,
    Clock,
    User,
    Bot,
    ChevronRight,
    BarChart3,
    Target,
    Brain,
    Zap,
    MessageSquare,
    Eye,
    CheckCircle2,
    Loader2,
    Circle,
  } from "lucide-svelte"

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  const messages = writable<Message[]>([])
  let input = ""
  let isLoading = false
  // let selectedMessageId = "" // Not currently used
  let placeholderIndex = 0
  let currentPlaceholder = ""
  let outputFormat = "executive"
  // let agentMode = "general" // Removed agent mode functionality
  let progressSteps: ProgressStep[] = []
  let currentProgress = 0

  // Animated placeholder examples
  const placeholderExamples = [
    "Compare Drift and Intercom's messaging and channel mix based on the last 30 days...",
    "How is Adobe marketing Firefly across its channels based on recent data...",
    "What influencer or social campaigns has Notion run recently...",
    "Map Figma's demand-gen strategy from 2022 to now...",
    "Analyze Stripe's developer marketing evolution over the past quarter...",
  ]

  // Quick start templates
  const quickStartTemplates = [
    {
      icon: BarChart3,
      title: "Company Snapshot",
      description: "Get a summary of performance, team, and competitors",
      prompt:
        "Provide a comprehensive company snapshot for [Company Name], including recent financial performance, leadership team overview, main competitors, and key business metrics.",
    },
    {
      icon: Target,
      title: "Go-to-Market Audit",
      description: "Evaluate positioning, messaging, channels, and campaigns",
      prompt:
        "Analyze [Company Name]'s go-to-market strategy including their positioning, messaging, marketing channels, recent campaigns, and overall effectiveness in reaching their target audience.",
    },
    {
      icon: Brain,
      title: "Brand Perception & Category",
      description: "Analyze how a brand is perceived in its space",
      prompt:
        "Research [Company Name]'s brand perception, category positioning, competitive differentiation, and customer sentiment. Include analysis of their brand identity and market perception.",
    },
  ]

  // Output format options
  const outputFormats = [
    {
      value: "executive",
      label: "Executive Summary",
      description: "Bullet points + key insights",
    },
    {
      value: "slide-ready",
      label: "Slide-ready",
      description: "Sections + headers for export",
    },
    {
      value: "battlecard",
      label: "Competitive Battlecard",
      description: "Strategic comparison format",
    },
  ]

  // Removed filter options - functionality not needed

  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    // Add output format context to the message with enhanced format-specific instructions
    let formatPrefix = ""
    if (outputFormat === "executive") {
      formatPrefix =
        "[Executive Summary Format] Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly. Use clear sections and numbered citations. "
    } else if (outputFormat === "slide-ready") {
      formatPrefix =
        "[Slide-ready Format] Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides. Include numbered citations for all claims. "
    } else if (outputFormat === "battlecard") {
      formatPrefix =
        "[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons. Include numbered citations and competitive analysis. "
    }

    const userMessage = formatPrefix + input.trim()
    input = ""
    isLoading = true

    // Initialize progress steps
    progressSteps = [
      {
        id: 1,
        title: "Initial Analysis",
        description: "Analyzing research request...",
        status: "pending",
      },
      {
        id: 2,
        title: "Web Search",
        description: "Conducting comprehensive search...",
        status: "pending",
      },
      {
        id: 3,
        title: "Financial Analysis",
        description: "Gathering financial data...",
        status: "pending",
      },
      {
        id: 4,
        title: "Market Research",
        description: "Analyzing market position...",
        status: "pending",
      },
      {
        id: 5,
        title: "Report Generation",
        description: "Creating research report...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    try {
      // Use streaming for progress updates
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/researcher?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.type === "final_response") {
                  // Add assistant response to chat
                  const assistantMessageId = generateId()
                  messages.update((msgs) => [
                    ...msgs,
                    {
                      id: assistantMessageId,
                      role: "assistant",
                      content: data.response,
                      timestamp: new Date(),
                      isReport: true,
                    },
                  ])
                } else if (data.step) {
                  // Update progress
                  currentProgress = data.progress
                  progressSteps = progressSteps.map((step) => {
                    if (step.id === data.step) {
                      return {
                        ...step,
                        status: data.status,
                        description: data.action,
                      }
                    } else if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    }
                    return step
                  })
                }
              } catch (e) {
                console.error("Error parsing SSE data:", e)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Error sending message:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content:
            "I apologize, but Athena encountered an error while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ])
    } finally {
      isLoading = false
      // Reset progress
      progressSteps = []
      currentProgress = 0
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function handleTemplateClick(template: (typeof quickStartTemplates)[0]) {
    input = template.prompt
    // Focus the textarea
    const textarea = document.querySelector("textarea")
    if (textarea) {
      textarea.focus()
    }
  }

  // Removed toggleFilters function - not needed

  function extractInsights(content: string): {
    summary: string
    insights: string[]
    badges: string[]
  } {
    // Simple insight extraction - in a real app this would be more sophisticated
    const lines = content.split("\n").filter((line) => line.trim())
    const summary = lines.slice(0, 2).join(" ").substring(0, 200) + "..."

    const insights = lines
      .filter(
        (line) =>
          line.includes("key") ||
          line.includes("important") ||
          line.includes("significant"),
      )
      .slice(0, 3)

    const badges = []
    if (content.includes("growth") || content.includes("increase"))
      badges.push("↑ Trending")
    if (content.includes("insight") || content.includes("analysis"))
      badges.push("💡 Insight")
    if (content.includes("challenge") || content.includes("weakness"))
      badges.push("⚠ Weakness")

    return { summary, insights, badges }
  }

  // Animated placeholder effect
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]

    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 4000)

    return () => clearInterval(interval)
  })

  function downloadAsMarkdown(message: Message) {
    const companyName = extractCompanyName(message.content)
    const timestamp = message.timestamp.toISOString().split("T")[0]
    const filename = `${companyName || "research-report"}-${timestamp}.md`

    const markdownContent = `# Marketing Research Report by Athena
**Generated on:** ${message.timestamp.toLocaleDateString()}
**Time:** ${message.timestamp.toLocaleTimeString()}
**Format:** ${outputFormat.charAt(0).toUpperCase() + outputFormat.slice(1)}

---

${message.content}

---

*Report generated by Athena - Your AI Market Researcher*
`

    const blob = new Blob([markdownContent], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  function extractCompanyName(content: string): string {
    // Simple extraction - looks for company name in the first line or headers
    const lines = content.split("\n")
    for (const line of lines.slice(0, 5)) {
      if (line.includes("Company:") || line.includes("Company Name:")) {
        return (
          line
            .split(":")[1]
            ?.trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
      if (
        line.startsWith("# ") &&
        !line.includes("Research") &&
        !line.includes("Report")
      ) {
        return (
          line
            .replace("# ", "")
            .trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
    }
    return ""
  }

  function formatContent(content: string): string {
    // Enhanced markdown to HTML conversion for professional display
    let formatted = content
      // Headers
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>',
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>',
      )
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>',
      )
      .replace(
        /^#### (.*$)/gim,
        '<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>',
      )

      // Bold text
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-bold" style="color: var(--foreground);">$1</strong>',
      )

      // Italic text
      .replace(
        /\*(.*?)\*/g,
        '<em class="italic" style="color: var(--muted-foreground);">$1</em>',
      )

      // Code blocks
      .replace(
        /```([\s\S]*?)```/g,
        '<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>',
      )

      // Inline code
      .replace(
        /`([^`]+)`/g,
        '<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>',
      )

      // Tables
      .replace(/\|(.+)\|/g, (match) => {
        const cells = match
          .split("|")
          .filter((cell) => cell.trim())
          .map((cell) => cell.trim())
        return (
          "<tr>" +
          cells
            .map(
              (cell) =>
                `<td class="border border-border px-3 py-2" style="color: var(--foreground);">${cell}</td>`,
            )
            .join("") +
          "</tr>"
        )
      })

      // Lists
      .replace(
        /^[\s]*[-*+] (.+)$/gim,
        '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>',
      )
      .replace(
        /^[\s]*\d+\. (.+)$/gim,
        '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: decimal;">$1</li>',
      )

      // Blockquotes
      .replace(
        /^> (.+)$/gim,
        '<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>',
      )

      // Links
      .replace(
        /\[([^\]]+)\]\(([^)]+)\)/g,
        '<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>',
      )

      // Line breaks and paragraphs
      .replace(
        /\n\n/g,
        '</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">',
      )
      .replace(/\n/g, "<br>")

    // Wrap content in paragraph if it doesn't start with a block element
    if (
      !formatted.startsWith("<h") &&
      !formatted.startsWith("<p") &&
      !formatted.startsWith("<ul") &&
      !formatted.startsWith("<ol") &&
      !formatted.startsWith("<blockquote")
    ) {
      formatted =
        '<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">' +
        formatted +
        "</p>"
    }

    // Wrap lists in proper ul/ol tags
    formatted = formatted.replace(/(<li[^>]*>.*?<\/li>)/gs, (match) => {
      if (match.includes("list-style-type: disc")) {
        return '<ul class="mb-4">' + match + "</ul>"
      } else if (match.includes("list-style-type: decimal")) {
        return '<ol class="mb-4">' + match + "</ol>"
      }
      return match
    })

    // Wrap table rows in table
    if (formatted.includes("<tr>")) {
      formatted = formatted.replace(
        /(<tr>.*?<\/tr>)/gs,
        '<table class="w-full border-collapse border border-border my-4">$1</table>',
      )
    }

    return formatted
  }

  // Auto-scroll to bottom when new messages are added
  $: if ($messages.length > 0) {
    setTimeout(() => {
      const messagesContainer = document.querySelector(".messages-container")
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 100)
  }

  // Removed - replaced with quickStartTemplates
</script>

<svelte:head>
  <title>Athena - AI Market Researcher</title>
</svelte:head>

<div class="h-screen flex flex-col" style="background: var(--background);">
  <!-- Header -->
  <div
    class="border-b-2 flex-shrink-0"
    style="border-color: var(--border); background: var(--background);"
  >
    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div
            class="w-12 h-12 flex items-center justify-center border-2"
            style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <Building2
              class="w-6 h-6"
              style="color: var(--primary-foreground);"
            />
          </div>
          <div>
            <h1
              class="text-3xl font-black flex items-center gap-3"
              style="color: var(--foreground);"
            >
              <Zap class="w-8 h-8" style="color: var(--primary);" />
              Athena
            </h1>
            <p
              class="text-lg font-medium"
              style="color: var(--muted-foreground);"
            >
              Your AI market researcher
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div
            class="flex items-center space-x-2 px-4 py-2 border-2"
            style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <TrendingUp
              class="w-4 h-4"
              style="color: var(--accent-foreground);"
            />
            <span
              class="text-sm font-bold"
              style="color: var(--accent-foreground);">Real-time Data</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="max-w-7xl px-6 lg:px-8 py-4">
    <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
      <a
        href="/dashboard/{$page.params.envSlug}"
        class="hover:text-foreground transition-colors"
      >
        Dashboard
      </a>
      <ChevronRight class="w-4 h-4" />
      <span class="text-foreground font-medium">Athena</span>
    </nav>
  </div>

  <div
    class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"
  >
    <!-- Full Width Conversation Panel -->
    <div class="h-full">
      <div
        class="card-brutal p-0 chat-container h-full"
        style="background: var(--card);"
      >
        <!-- Messages Area -->
        <div class="messages-wrapper messages-container">
          <div class="space-y-6">
            {#if $messages.length === 0}
              <div class="text-center py-8">
                <div class="flex items-center justify-center gap-2 mb-6">
                  <Zap class="w-6 h-6" style="color: var(--primary);" />
                  <h3
                    class="text-2xl font-bold"
                    style="color: var(--foreground);"
                  >
                    Ready to Research
                  </h3>
                </div>
                <p
                  class="font-medium mb-8 max-w-2xl mx-auto"
                  style="color: var(--muted-foreground);"
                >
                  Get marketing intelligence on any company. Choose a template
                  below or ask your custom question.
                </p>

                <!-- Quick Start Template Cards -->
                <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
                  {#each quickStartTemplates as template}
                    <button
                      on:click={() => handleTemplateClick(template)}
                      class="template-card p-6 border-2 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg text-left group"
                      style="background: var(--card); border-color: var(--border); border-radius: 0.5rem;"
                    >
                      <div class="mb-3">
                        <svelte:component
                          this={template.icon}
                          class="w-8 h-8"
                          style="color: var(--primary);"
                        />
                      </div>
                      <h4
                        class="text-lg font-bold mb-2 group-hover:text-primary transition-colors"
                        style="color: var(--foreground);"
                      >
                        {template.title}
                      </h4>
                      <p
                        class="text-sm leading-relaxed"
                        style="color: var(--muted-foreground);"
                      >
                        {template.description}
                      </p>
                      <div
                        class="flex items-center gap-1 mt-3 text-primary opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <span class="text-xs font-bold">Use Template</span>
                        <ChevronRight class="w-3 h-3" />
                      </div>
                    </button>
                  {/each}
                </div>
              </div>
            {/if}

            {#each $messages as message}
              <div
                class="flex gap-4 {message.role === 'user'
                  ? 'flex-row-reverse'
                  : ''}"
              >
                <div
                  class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                  style="background: var(--{message.role === 'user'
                    ? 'primary'
                    : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm);"
                >
                  {#if message.role === "user"}
                    <User
                      class="w-5 h-5"
                      style="color: var(--primary-foreground);"
                    />
                  {:else}
                    <Bot
                      class="w-5 h-5"
                      style="color: var(--secondary-foreground);"
                    />
                  {/if}
                </div>

                <div class="flex-1 max-w-3xl">
                  <div class="flex items-center gap-2 mb-2">
                    <span
                      class="text-sm font-bold"
                      style="color: var(--foreground);"
                    >
                      {message.role === "user" ? "You" : "Athena"}
                    </span>
                    <div class="flex items-center gap-1">
                      <Clock
                        class="w-3 h-3"
                        style="color: var(--muted-foreground);"
                      />
                      <span
                        class="text-xs"
                        style="color: var(--muted-foreground);"
                      >
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    {#if message.role === "assistant" && message.isReport}
                      <button
                        on:click={() => downloadAsMarkdown(message)}
                        class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                        title="Download as Markdown"
                      >
                        <Download class="w-3 h-3" />
                        Download
                      </button>
                    {/if}
                  </div>

                  {#if message.role === "user"}
                    <div
                      class="p-4 border-2"
                      style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                    >
                      <p
                        class="font-medium"
                        style="color: var(--primary-foreground);"
                      >
                        {message.content}
                      </p>
                    </div>
                  {:else}
                    {@const insights = extractInsights(message.content)}
                    <div
                      class="border-2"
                      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;"
                    >
                      <!-- TL;DR Summary -->
                      {#if insights.summary}
                        <div class="p-4 border-b-2 border-border bg-muted/50">
                          <div class="flex items-start gap-2">
                            <span
                              class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground rounded"
                              >TL;DR</span
                            >
                            <div
                              class="text-sm font-medium formatted-summary"
                              style="color: var(--foreground);"
                            >
                              {@html formatContent(insights.summary)}
                            </div>
                          </div>
                        </div>
                      {/if}

                      <!-- Badges -->
                      {#if insights.badges.length > 0}
                        <div class="px-6 pt-4 pb-2">
                          <div class="flex flex-wrap gap-2">
                            {#each insights.badges as badge}
                              <span
                                class="text-xs font-bold px-2 py-1 border border-border rounded"
                                style="background: var(--accent); color: var(--accent-foreground);"
                              >
                                {badge}
                              </span>
                            {/each}
                          </div>
                        </div>
                      {/if}

                      <!-- Main Content -->
                      <div class="p-6">
                        <div class="formatted-content max-w-none">
                          {@html formatContent(message.content)}
                        </div>
                      </div>

                      <!-- Follow-up Actions -->
                      <div class="px-6 pb-4 border-t border-border">
                        <div class="flex flex-wrap gap-2 mt-4">
                          <button
                            on:click={() =>
                              (input = `Compare this analysis with their main competitor`)}
                            class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                          >
                            <Eye class="w-3 h-3" />
                            Compare with competitor
                          </button>
                          <button
                            on:click={() =>
                              (input = `Add visual charts and graphs to this analysis`)}
                            class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                          >
                            <BarChart3 class="w-3 h-3" />
                            Add visuals
                          </button>
                          <button
                            on:click={() =>
                              (input = `Turn this analysis into presentation slides`)}
                            class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                          >
                            <MessageSquare class="w-3 h-3" />
                            Turn into slides
                          </button>
                        </div>
                      </div>
                    </div>
                  {/if}
                </div>
              </div>
            {/each}

            {#if isLoading}
              <div class="flex gap-4">
                <div
                  class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                  style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                >
                  <Bot
                    class="w-5 h-5"
                    style="color: var(--secondary-foreground);"
                  />
                </div>
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span
                      class="text-sm font-bold"
                      style="color: var(--foreground);">Athena</span
                    >
                    <span
                      class="text-xs"
                      style="color: var(--muted-foreground);"
                      >Conducting comprehensive research...</span
                    >
                  </div>

                  <!-- Progress Tracking Section -->
                  {#if progressSteps.length > 0}
                    <div
                      class="border-2 p-6"
                      style="background: var(--card); border-color: var(--border);"
                      transition:slide={{ duration: 300 }}
                    >
                      <h4
                        class="font-bold mb-4"
                        style="color: var(--foreground);"
                      >
                        Research Progress
                      </h4>

                      <!-- Progress Bar -->
                      <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                          <span
                            class="text-sm font-medium"
                            style="color: var(--foreground);"
                          >
                            Overall Progress
                          </span>
                          <span
                            class="text-sm font-bold"
                            style="color: var(--primary);"
                          >
                            {currentProgress}%
                          </span>
                        </div>
                        <div
                          class="w-full h-2 border-2 overflow-hidden"
                          style="background: var(--muted); border-color: var(--border);"
                        >
                          <div
                            class="h-full transition-all duration-500 ease-out"
                            style="background: var(--primary); width: {currentProgress}%;"
                          ></div>
                        </div>
                      </div>

                      <!-- Progress Steps -->
                      <div class="space-y-4">
                        {#each progressSteps as step (step.id)}
                          <div
                            class="flex items-start gap-3"
                            transition:slide={{ duration: 300 }}
                          >
                            <div class="flex-shrink-0 mt-0.5">
                              {#if step.status === "completed"}
                                <div transition:fade={{ duration: 200 }}>
                                  <CheckCircle2
                                    class="w-5 h-5 animate-scale-in"
                                    style="color: var(--primary);"
                                  />
                                </div>
                              {:else if step.status === "active"}
                                <Loader2
                                  class="w-5 h-5 animate-spin"
                                  style="color: var(--primary);"
                                />
                              {:else}
                                <Circle
                                  class="w-5 h-5 opacity-30"
                                  style="color: var(--muted-foreground);"
                                />
                              {/if}
                            </div>
                            <div class="flex-1">
                              <h4
                                class="text-sm font-bold mb-1"
                                style="color: {step.status === 'pending'
                                  ? 'var(--muted-foreground)'
                                  : 'var(--foreground)'};{step.status ===
                                'pending'
                                  ? 'opacity: 0.5'
                                  : ''}"
                              >
                                {step.title}
                              </h4>
                              <p
                                class="text-xs"
                                style="color: {step.status === 'pending'
                                  ? 'var(--muted-foreground)'
                                  : 'var(--muted-foreground)'};{step.status ===
                                'pending'
                                  ? 'opacity: 0.5'
                                  : ''}"
                              >
                                {step.description}
                              </p>
                            </div>
                          </div>
                        {/each}
                      </div>
                    </div>
                  {:else}
                    <!-- Fallback loading animation -->
                    <div
                      class="p-4 border-2"
                      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"
                    >
                      <div class="flex items-center space-x-2">
                        <div
                          class="w-2 h-2 rounded-full animate-pulse"
                          style="background: var(--primary);"
                        ></div>
                        <div
                          class="w-2 h-2 rounded-full animate-pulse animation-delay-2000"
                          style="background: var(--primary);"
                        ></div>
                        <div
                          class="w-2 h-2 rounded-full animate-pulse animation-delay-4000"
                          style="background: var(--primary);"
                        ></div>
                        <span
                          class="text-sm font-medium"
                          style="color: var(--muted-foreground);"
                          >Initializing research process...</span
                        >
                      </div>
                    </div>
                  {/if}
                </div>
              </div>
            {/if}
          </div>
        </div>

        <!-- Spotlight-Style Input Area -->
        <div class="input-wrapper p-6">
          <!-- Output Format Selector -->
          <div class="flex items-center gap-2 mb-4">
            <span
              class="text-sm font-bold"
              style="color: var(--muted-foreground);">Format:</span
            >
            <select
              bind:value={outputFormat}
              class="px-3 py-1 text-sm border-2 border-border bg-card text-foreground font-medium rounded"
              style="border-radius: 0.375rem;"
            >
              {#each outputFormats as format}
                <option value={format.value}>{format.label}</option>
              {/each}
            </select>
          </div>

          <div class="relative">
            <div class="spotlight-input-container">
              <textarea
                bind:value={input}
                on:keydown={handleKeyDown}
                placeholder={currentPlaceholder}
                class="spotlight-input flex-1 resize-none min-h-[120px] p-6 pr-32 text-lg"
                disabled={isLoading}
              ></textarea>
              <button
                on:click={sendMessage}
                disabled={!input.trim() || isLoading}
                class="spotlight-button"
              >
                {#if isLoading}
                  <div
                    class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"
                  ></div>
                {:else}
                  <Search class="w-5 h-5" />
                {/if}
                <span class="font-bold">Research</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
